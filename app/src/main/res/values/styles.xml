<resources>
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>

    </style>

    <style name="loading_dialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowFrame">@null</item><!--边框-->
        <item name="android:windowIsFloating">true</item><!--是否浮现在activity之上-->
        <item name="android:windowIsTranslucent">true</item><!--半透明-->
        <item name="android:windowNoTitle">true</item><!--无标题-->
        <item name="android:windowBackground">@android:color/transparent
        </item> <!--背景透明   去掉背景色边框也就去掉了 -->
        <item name="android:backgroundDimEnabled">false</item><!--模糊-->
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimAmount">0</item>
    </style>
</resources>
