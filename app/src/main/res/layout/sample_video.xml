<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <FrameLayout
        android:id="@+id/surface_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center">

        <RelativeLayout
                android:id="@+id/thumb"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#000000"
                android:scaleType="fitCenter">
            <ImageView
                    android:id="@+id/back"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:paddingLeft="10dp"
                    android:scaleType="centerInside"
                    android:src="@drawable/video_back"/>
        </RelativeLayout>
    </FrameLayout>


    <ProgressBar
        android:id="@+id/bottom_progressbar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="1.5dp"
        android:layout_alignParentBottom="true"
        android:max="100"
        android:progressDrawable="@drawable/video_progress" />

    <ImageView
        android:id="@+id/back_tiny"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginLeft="6dp"
        android:layout_marginTop="6dp"
        android:visibility="gone" />


    <ImageView
        android:id="@+id/small_close"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:paddingLeft="10dp"
        android:paddingTop="10dp"
        android:scaleType="centerInside"
        android:src="@drawable/video_small_close"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/lock_screen"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="50dp"
        android:scaleType="centerInside"
        android:src="@drawable/unlock"
        android:visibility="gone" />

    <RelativeLayout
        android:id="@+id/preview_layout"
        android:layout_width="@dimen/seek_bar_image"
        android:layout_height="100dp"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="30dp"
        android:visibility="gone" />

</RelativeLayout>
