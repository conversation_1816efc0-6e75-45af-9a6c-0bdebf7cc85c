<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/mainBgColor">

    <!-- Left side container -->
    <LinearLayout
        android:id="@+id/left_container"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/dp_32"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/right_container"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.6">

        <!-- Back button -->
        <Button
            android:id="@+id/btn_back"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_50"
            android:text="@string/back"
            android:textSize="@dimen/sp_18"
            android:background="@drawable/bg_shape_common"
            android:textColor="@android:color/black"
            android:enabled="false" />

        <!-- Payment information -->
        <TextView
            android:id="@+id/tv_payment_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/app_payment_info"
            android:textSize="@dimen/sp_20"
            android:textColor="@android:color/white"
            android:layout_marginTop="@dimen/dp_60"
            android:gravity="start" />

        <!-- Instructions -->
        <TextView
            android:id="@+id/tv_instructions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/app_payment_instructions"
            android:textSize="@dimen/sp_18"
            android:textColor="@color/white_c9"
            android:layout_marginTop="@dimen/dp_32"
            android:gravity="start"
            android:lineSpacingExtra="@dimen/dp_4" />

        <!-- Status text -->
        <TextView
            android:id="@+id/tv_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Waiting for payment..."
            android:textSize="@dimen/sp_18"
            android:textColor="@color/main_color"
            android:layout_marginTop="@dimen/dp_32"
            android:gravity="start" />

    </LinearLayout>

    <!-- Right side container with QR Code -->
    <LinearLayout
        android:id="@+id/right_container"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="@dimen/dp_32"
        app:layout_constraintStart_toEndOf="@id/left_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.4">

        <!-- QR Code title -->
        <TextView
            android:id="@+id/tv_qr_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Scan QR Code"
            android:textSize="@dimen/sp_24"
            android:textColor="@android:color/white"
            android:textStyle="bold"
            android:layout_marginBottom="@dimen/dp_24" />

        <!-- QR Code container -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/qr_container"
            android:layout_width="@dimen/dp_250"
            android:layout_height="@dimen/dp_250"
            android:background="@android:color/white"
            android:padding="@dimen/dp_16">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_qr_code"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- QR Code info -->
        <TextView
            android:id="@+id/tv_qr_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Use your mobile payment app"
            android:textSize="@dimen/sp_14"
            android:textColor="@color/white_c9"
            android:layout_marginTop="@dimen/dp_16"
            android:gravity="center" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
