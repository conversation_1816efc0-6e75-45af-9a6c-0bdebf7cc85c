<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/mainBgColor">

    <!-- Left side container -->
    <LinearLayout
        android:id="@+id/left_container"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/dp_32"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/right_container"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.6">

        <!-- Back button -->
        <Button
            android:id="@+id/btn_back"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_50"
            android:text="@string/back"
            android:textSize="@dimen/sp_18"
            android:background="@drawable/bg_shape_common"
            android:textColor="@android:color/black"
            android:enabled="false" />

        <!-- Payment information -->
        <TextView
            android:id="@+id/tv_payment_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/terminal_payment_info"
            android:textSize="@dimen/sp_20"
            android:textColor="@android:color/white"
            android:layout_marginTop="@dimen/dp_60"
            android:gravity="start" />

        <!-- Status text -->
        <TextView
            android:id="@+id/tv_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/terminal_status_waiting"
            android:textSize="@dimen/sp_18"
            android:textColor="@color/main_color"
            android:layout_marginTop="@dimen/dp_32"
            android:gravity="start" />

        <!-- Additional info -->
        <TextView
            android:id="@+id/tv_additional_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Please wait while we process your payment..."
            android:textSize="@dimen/sp_16"
            android:textColor="@color/white_c9"
            android:layout_marginTop="@dimen/dp_24"
            android:gravity="start" />

    </LinearLayout>

    <!-- Right side container -->
    <LinearLayout
        android:id="@+id/right_container"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="@dimen/dp_32"
        app:layout_constraintStart_toEndOf="@id/left_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.4">

        <!-- Large instruction text -->
        <TextView
            android:id="@+id/tv_instruction"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Insert or Tap\nYour Card"
            android:textSize="@dimen/sp_36"
            android:textColor="@android:color/white"
            android:textStyle="bold"
            android:gravity="center"
            android:lineSpacingExtra="@dimen/dp_8" />

        <!-- Progress indicator -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_80"
            android:layout_marginTop="@dimen/dp_40"
            android:indeterminateTint="@color/main_color" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
