<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <ImageView
        android:id="@+id/image"
        android:visibility="gone"
        android:scaleType="centerInside"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:keepScreenOn="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    
    <com.stwpower.player.player.MyVideoPlayer
        android:id="@+id/standardGSYVideoPlayer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:keepScreenOn="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    
    <include
        layout="@layout/layout_qr_code_80_3"/>

</androidx.constraintlayout.widget.ConstraintLayout>