<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/image"
        android:visibility="gone"
        android:scaleType="centerInside"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:keepScreenOn="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    
    <com.stwpower.player.player.MyVideoPlayer
        android:id="@+id/standardGSYVideoPlayer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:keepScreenOn="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    
    <include
        layout="@layout/layout_qr_code_120"/>



</androidx.constraintlayout.widget.ConstraintLayout>