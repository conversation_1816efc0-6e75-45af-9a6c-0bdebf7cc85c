<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/mainBgColor">

    <!-- Language buttons at top -->
    <LinearLayout
        android:id="@+id/language_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/dp_32"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="@dimen/dp_32">

        <Button
            android:id="@+id/btn_language_en"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_40"
            android:text="@string/language_en"
            android:textSize="@dimen/sp_14"
            android:layout_marginEnd="@dimen/dp_8"
            android:background="@drawable/bg_shape_common"
            android:textColor="@android:color/black" />

        <Button
            android:id="@+id/btn_language_zh"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_40"
            android:text="@string/language_zh"
            android:textSize="@dimen/sp_14"
            android:layout_marginEnd="@dimen/dp_8"
            android:background="@drawable/bg_shape_common"
            android:textColor="@android:color/black" />

        <Button
            android:id="@+id/btn_language_de"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_40"
            android:text="@string/language_de"
            android:textSize="@dimen/sp_14"
            android:background="@drawable/bg_shape_common"
            android:textColor="@android:color/black" />

    </LinearLayout>

    <!-- Title -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/home_title"
        android:textSize="@dimen/sp_48"
        android:textColor="@android:color/white"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/payment_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Payment buttons container -->
    <LinearLayout
        android:id="@+id/payment_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <Button
            android:id="@+id/btn_pay_terminal"
            android:layout_width="@dimen/dp_300"
            android:layout_height="@dimen/dp_120"
            android:text="@string/pay_with_terminal"
            android:textSize="@dimen/sp_24"
            android:textStyle="bold"
            android:layout_marginEnd="@dimen/dp_60"
            android:background="@drawable/bg_shape_common"
            android:textColor="@android:color/black" />

        <Button
            android:id="@+id/btn_pay_app"
            android:layout_width="@dimen/dp_300"
            android:layout_height="@dimen/dp_120"
            android:text="@string/pay_with_app"
            android:textSize="@dimen/sp_24"
            android:textStyle="bold"
            android:background="@drawable/bg_shape_common"
            android:textColor="@android:color/black" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
