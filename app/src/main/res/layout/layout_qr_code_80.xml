<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_80"
    android:layout_height="@dimen/dp_80"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    android:layout_marginStart="@dimen/dp_16"
    android:layout_marginBottom="@dimen/dp_16"
    android:background="@drawable/bg_shape_common">
    
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_qr_code"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="@dimen/dp_4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_qr_code"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="10sp"
        android:textColor="@android:color/black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_qr_code"
        android:layout_marginBottom="@dimen/dp_4"/>
</androidx.constraintlayout.widget.ConstraintLayout>