<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/image"
        android:visibility="gone"
        android:layout_width="0pt"
        android:layout_height="wrap_content"
        android:scaleType="centerInside"
        android:layout_centerInParent="true"
        android:keepScreenOn="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
         />

    <com.stwpower.player.player.MyVideoPlayer
        android:id="@+id/standardGSYVideoPlayer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:keepScreenOn="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <include layout="@layout/layout_qr_code_80"/>



</androidx.constraintlayout.widget.ConstraintLayout>