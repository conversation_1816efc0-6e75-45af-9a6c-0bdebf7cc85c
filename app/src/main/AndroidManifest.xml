<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
<!--    <uses-permission android:name="android.permission.RECOVERY" />-->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- 网络状态检查 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 锁屏下继续缓冲 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- 程序自启动权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- 悬浮窗 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> <!-- 启用通知栏需使用 -->
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.INSTALL_PACKAGES" /> <!-- 安装apk -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- 安装apk -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!--安卓11外部文件权限-->
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />

    <permission
        android:name="com.stwpower.player.RECEIVE_DATA"
        android:protectionLevel="signature" />
    <application
        android:name="com.stwpower.player.base.MyApp"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">

<!--        <activity android:name=".MainActivity" />-->
<!--        PlayActivity-->
        <activity
            android:name="com.stwpower.player.MainActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="portrait"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <!--                <category android:name="android.intent.category.DEFAULT" />-->
                <!--                <category android:name="android.intent.category.HOME" />-->
            </intent-filter>
        </activity>

        <receiver
            android:name="com.stwpower.player.BootUpReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
        <receiver android:name="com.stwpower.player.receiver.UpdateReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED"/>
            </intent-filter>
        </receiver>
        <receiver android:name="com.stwpower.player.receiver.RestartAppReceiver" />
        <receiver
            android:name=".receiver.ReceiveDataReceiver"
            android:enabled="true"
            android:exported="true"
            android:permission="com.stwpower.player.RECEIVE_DATA">
            <intent-filter>
                <action android:name="com.stwpower.player.ACTION_RECEIVE_DATA" />
            </intent-filter>
        </receiver>

    </application>

</manifest>