package com.stwpower.player.base;

import android.app.Application;
import android.content.ComponentCallbacks2;

import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.text.TextUtils;

import androidx.work.ExistingPeriodicWorkPolicy;
import androidx.work.PeriodicWorkRequest;
import androidx.work.WorkManager;
import com.blankj.utilcode.util.Utils;
import com.stwpower.player.BuildConfig;
import com.stwpower.player.bean.PopupMessage;
import com.stwpower.player.worker.UpdateWorker;
import com.stwpower.player.constant.Constants;
import com.stwpower.player.utils.LogHelper;
import com.stripe.stripeterminal.external.models.DiscoveryMethod;
import com.tencent.bugly.crashreport.CrashReport;

import org.apache.log4j.Logger;

import java.io.*;
import java.util.concurrent.TimeUnit;
import android.content.res.Configuration;
import java.util.Locale;
import android.content.Intent;
import android.util.Log;
/**
 * 作者：ly-xuxiaolong
 * 版本：1.0
 * 创建日期：2020/3/9
 * 描述：
 * 修订历史：
 */
public class MyApp extends Application{

    public static boolean isUpdatingAPP = false;
    public static MyApp instance;
    private Logger logger = Logger.getLogger(getClass());
    // public static Device device;
    //显示方向
    public static boolean protrait = true;
    //读取imei方式
    public static String fno_type = "b";
    //请求url
    public static String baseUrl = "http://ntc.player.stwpower.com:8081/power_bank";
    //拼接二维码的url
    public static String qrCodeUrl = "http://stwpower.com//appWeb/store?id=%s";
    //设备类型 b6/b12/b24 c12/c24
    public static String device_type = "b12";
    public static boolean isSimulated = false;
    public static DiscoveryMethod discoveryMethod = DiscoveryMethod.USB; //固定
    public static String secretKey; //从配置文件中获取
    public static final String TERMINAL_TAG = "terminal";
    public static final String PLAYER_TAG = "player";
    public static final String NAYAX_TAG = "nayax";
    public static final String NEW_BACK = "back";
    public static final String UNIQUE_WORK_NAME = "app_update_work";
    private static String language = "en";
    public static String posType = "stripe";
    @Override
    public void onCreate(){
        secretKey = null;
        try {
            PackageInfo packageInfo = getPackageManager().getPackageInfo(getPackageName(), 0);
            int versionCode = packageInfo.versionCode;
            String versionName = packageInfo.versionName;
            LogHelper.INSTANCE.i(PLAYER_TAG, "版本号: " + versionCode + "，版本名: " + versionName);
        } catch (PackageManager.NameNotFoundException e) {
            LogHelper.INSTANCE.e(PLAYER_TAG, "Package name not found: " + e.getMessage());
        }
        schedulePeriodicWork();
        instance = this;
        // device = getDevices();
        File configFile = new File(Constants.getConfigDirectory()+"config.txt");
        FileInputStream fis = null;
        if (TextUtils.equals(BuildConfig.BUILD_TYPE, "release")) {
            try {
                fis = new FileInputStream(configFile);
                InputStreamReader reader = new InputStreamReader(fis);
                BufferedReader bis = new BufferedReader(reader);
                String s = "";
                while((s = bis.readLine()) != null){
                    String[] split = s.split(":");
                    switch (split[0]){
                        case "baseUrl":
                            baseUrl = s.substring(8);
                            break;
                        case "qrCodeUrl":
                            qrCodeUrl = s.substring(10);
                            break;
                        case "device_type":
                            device_type = split[1];
                            switch (split[1]){
                                case "b6":
                                case "b6_2":
                                case "b12":
                                    protrait = false;
                                    fno_type = "b";
                                    break;
                                case "b24":
                                    protrait = true;
                                    fno_type = "b";
                                    break;
                                case "c12":
                                    protrait = false;
                                    fno_type = "c";
                                    break;
                                case "c24":
                                    protrait = true;
                                    fno_type = "c";
                                    break;
                            }
                            break;
                        case "secretKey":
                            secretKey = split[1];
                            break;
                        case "language":
                            language = split[1];
                            break;
                        case "posType":
                            posType = split[1];
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            protrait = false;
            fno_type = "b";
            baseUrl = "https://powerweb-stw.stwpower.com/power_bank";
            qrCodeUrl = "http://dnm.stwpower.com/appWeb/store?id=%s";
            device_type = "b24";
            secretKey = "u7hhaq1VycgXmilJ6k6BHUHENlwe5t5e";
            posType = "nayax";
        }
        PopupMessage.initialize();
        setLocale(language);
        // InputStreamReader reader = new InputStreamReader(fis);
        Utils.init(this);
        CrashReport.initCrashReport(getApplicationContext(), Constants.BUGLY_APPID, true);
        Thread.setDefaultUncaughtExceptionHandler(new Thread.UncaughtExceptionHandler() {
            @Override
            public void uncaughtException(Thread thread, Throwable ex) {
                // 1. 记录详细错误信息
                String stackTrace = Log.getStackTraceString(ex);
                LogHelper.INSTANCE.e(MyApp.PLAYER_TAG, "未捕获异常：" + ex.getMessage());
                LogHelper.INSTANCE.e(MyApp.PLAYER_TAG, "堆栈信息：" + stackTrace);
                logger.error("线程 " + thread.getName() + " 发生未捕获异常", ex);

                // 2. 上报到 Bugly
                CrashReport.postCatchedException(ex);

                // 3. 保存当前状态（如有必要）
                saveApplicationState();

                // 4. 重启应用
                restartApp();
            }
        });
        super.onCreate();
    }

    public static MyApp getApp(){
        return instance;
    }

    private void schedulePeriodicWork() {
        PeriodicWorkRequest updateRequest = new PeriodicWorkRequest.Builder(UpdateWorker.class, 60 * 24, TimeUnit.MINUTES)
                .build();

        WorkManager.getInstance(this)
                .enqueueUniquePeriodicWork(
                        UNIQUE_WORK_NAME,
                        ExistingPeriodicWorkPolicy.REPLACE, // 如果已经存在，保留现有的周期性工作
                        updateRequest
                );
    }

    @Override
    public void onLowMemory(){
        super.onLowMemory();
        LogHelper.INSTANCE.e(MyApp.PLAYER_TAG,"onLowMemory");
    }

    @Override
    public void onTerminate(){
        super.onTerminate();
        LogHelper.INSTANCE.e(MyApp.PLAYER_TAG,"onTerminate");
    }

    @Override
    public void onTrimMemory(int level){
        super.onTrimMemory(level);
        if (level >= ComponentCallbacks2.TRIM_MEMORY_MODERATE) {
            LogHelper.INSTANCE.e(MyApp.PLAYER_TAG,"onTrimMemory"+level);
        }
    }

    private void setLocale(String lang) {
        Locale locale = new Locale(lang);
        Locale.setDefault(locale);
        Configuration config = new Configuration();
        config.setLocale(locale);  // 使用 setLocale 而不是 config.locale，确保新API兼容性
        getResources().updateConfiguration(config, getResources().getDisplayMetrics());
    }

    private void saveApplicationState() {
        // 实现保存关键数据的逻辑
    }

    private void restartApp() {
        Intent intent = getPackageManager()
                .getLaunchIntentForPackage(getPackageName());
        if (intent != null) {
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        }
        android.os.Process.killProcess(android.os.Process.myPid());
    }
}
