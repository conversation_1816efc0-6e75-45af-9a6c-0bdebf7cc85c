package com.stwpower.player.player

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.Surface
import android.view.View
import android.widget.SeekBar
import com.blankj.utilcode.util.BusUtils
import com.stwpower.player.R
import com.stwpower.player.base.MyApp
import com.stwpower.player.bean.RuleInfo
import com.stwpower.player.constant.Constants
import com.stwpower.player.room.RuleDataBase
import com.stwpower.player.utils.DownLoadUtil
import com.stwpower.player.utils.LogHelper
import com.shuyu.gsyvideoplayer.GSYVideoBaseManager
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.shuyu.gsyvideoplayer.listener.GSYMediaPlayerListener
import com.shuyu.gsyvideoplayer.utils.Debuger
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer
import com.stwpower.player.even.CheckTaskEvent
import com.stwpower.player.even.DownloadFailedEvent
import com.stwpower.player.even.DownloadFinishEvent
import com.stwpower.player.even.ShowImageEvent
import com.stwpower.player.even.ShowVideoEvent
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*
import java.util.concurrent.Executors

class MyVideoPlayer : StandardGSYVideoPlayer {
    // 使用密封类来表示播放内容类型
    sealed class ContentType {
        object Image : ContentType()
        object Video : ContentType()
        object Local : ContentType()
    }

    // 使用数据类来封装播放状态
    private data class PlaybackState(
        var currentPosition: Int = 0,
        var isPlayLocalVideo: Boolean = true,
        var isPlayPicture: Boolean = false,
        var imgUrl: String = ""
    )

    private val playbackState = PlaybackState()
    private val data: MutableList<RuleInfo> = mutableListOf()
    private val rawData: MutableList<RuleInfo> = mutableListOf()
    private val oldAd: MutableList<Map<String, String>> = mutableListOf()
    private val newAd: MutableList<Map<String, String>> = mutableListOf()

    private val ruleDao by lazy {
        RuleDataBase.getInstance(MyApp.instance.applicationContext).ruleDao()
    }
    private val cachedThreadPool = Executors.newCachedThreadPool()
    private var mTmpManager: GSYVideoManager? = null

    constructor(context: Context?, fullFlag: Boolean?) : super(context, fullFlag) {}
    constructor(context: Context?) : super(context) {}
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {}

    /**
     * 播放器准备好之后触发（视频加载到播放器中）
     */
    override fun onPrepared() {
        LogHelper.i(MyApp.PLAYER_TAG, "onPrepared")
        super.onPrepared()
        hideAllView()
    }

    /**
     * 退出播放器 和 onError 时会触发，退出播放器重新打开之后也会触发（又调用了releaseTmpManager()，所以导致没播放）
     */
    override fun onCompletion() {
        LogHelper.i(MyApp.PLAYER_TAG, "onCompletion")
        try {
            onVideoReset()
        } catch (e: Exception) {
            LogHelper.e(MyApp.PLAYER_TAG, e.message + "")
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.sample_video
    }

    /**
     * 视频播放完成（GSYVideoPlayer 提供的更具体的回调）
     */
    override fun onAutoCompletion() {
        LogHelper.i(MyApp.PLAYER_TAG, "播放完成")
        hideAllView()
        releaseTmpManager()
        cachedThreadPool.execute {
            synchronized(MyApp.instance) {
                checkRule(ruleDao.allRule())
            }
        }
    }

    override fun startProgressTimer() {
        super.startProgressTimer()
    }

    private fun resolveVideoChange(url: String) {
        // 在工作线程中
        val handler = Handler(Looper.getMainLooper())
        handler.post {
            //创建临时管理器执行加载播放
            mTmpManager = GSYVideoManager.tmpInstance(gsyMediaPlayerListener)
            mTmpManager!!.initContext(context.applicationContext)
            resolveChangeUrl(url)
            mTmpManager!!.prepare(mUrl, mMapHeadData, mLooping, mSpeed, mCache, mCachePath, null)
        }
    }

    private fun resolveChangeUrl(url: String) {
        if (mTmpManager != null) {
            mOriginUrl = url
            mUrl = url
        }
    }

    /**
     * 具体错误信息可查看类 PlaybackException
     */
    override fun onError(what: Int, extra: Int) {
        /**
         * 出现4003时黑屏 Caused by a failure while trying to decode media samples（由尝试解码媒体样本时失败引起）
         */
        LogHelper.e(MyApp.PLAYER_TAG, "onError---$what---$extra")
        mTmpManager?.releaseMediaPlayer()
        onVideoReset()

        Handler(Looper.getMainLooper()).postDelayed({
            LogHelper.i(MyApp.PLAYER_TAG, "5s后重新初始化播放器")
            post {
                setUp(url, false, null, "")
                startPlayLogic()
            }

            cachedThreadPool.execute {
                synchronized(MyApp.instance) {
                    checkRule(ruleDao.allRule())
                }
            }
        }, 5 * 1000)
    }

    override fun changeUiToPlayingShow() {
        super.changeUiToPlayingShow()
        hideAllView()
    }

    override fun changeUiToPlayingClear() {
        super.changeUiToPlayingClear()
        hideAllView()
    }

    override fun changeUiToPlayingBufferingClear() {
        super.changeUiToPlayingBufferingClear()
        hideAllView()
    }

    override fun changeUiToPrepareingClear() {
        super.changeUiToPrepareingClear()
        hideAllView()
    }

    override fun changeUiToNormal() {
        super.changeUiToNormal()
        hideAllView()
    }

    override fun changeUiToPauseShow() {
        super.changeUiToPauseShow()
        hideAllView()
    }

    override fun changeUiToPlayingBufferingShow() {
        super.changeUiToPlayingBufferingShow()
        hideAllView()
    }

    override fun changeUiToCompleteShow() {
        super.changeUiToCompleteShow()
        hideAllView()
    }

    override fun changeUiToError() {
        super.changeUiToError()
        hideAllView()
    }

    override fun changeUiToCompleteClear() {
        super.changeUiToCompleteClear()
        hideAllView()
    }

    override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
        super.onProgressChanged(seekBar, progress, fromUser)
        LogHelper.e(MyApp.PLAYER_TAG, "onProgressChanged:$progress")
    }

    private fun hideAllView() {
        setViewShowState(mTopContainer, View.GONE)
        setViewShowState(mBottomProgressBar, View.GONE)
        setViewShowState(mBottomContainer, View.GONE)
        setViewShowState(mStartButton, View.GONE)
    }

    /**
     * 双击暂停/播放
     * 如果不需要，重载为空方法即可
     */
    override fun touchDoubleUp(e: MotionEvent) {}
    override fun startDismissControlViewTimer() {}

    override fun onTouch(v: View?, event: MotionEvent?): Boolean {
        return true
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
//        BusUtils.register(this)
        EventBus.getDefault().register(this)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
//        BusUtils.unregister(this)
        EventBus.getDefault().unregister(this)
    }

    @Synchronized
    private fun setPath() {
        LogHelper.i(MyApp.PLAYER_TAG, "播放位置：${playbackState.currentPosition}, data:$data")

        post {
            when {
                data.isEmpty() -> playLocalVideo()
                else -> {
                    val currentItem = data[playbackState.currentPosition]
                    when (getContentType(currentItem)) {
                        ContentType.Image -> showIMG()
                        ContentType.Video -> showVideo()
                        ContentType.Local -> playLocalVideo()
                    }
                }
            }
        }
    }

    private fun getContentType(ruleInfo: RuleInfo): ContentType {
        return when (ruleInfo.cover_type) {
            0 -> ContentType.Image
            1 -> ContentType.Video
            2 -> {
                val extension = ruleInfo.content.substringAfterLast(".", "")
                when (extension.lowercase()) {
                    "jpg", "png", "gif" -> ContentType.Image
                    "mp4" -> ContentType.Video
                    else -> ContentType.Local
                }
            }

            else -> ContentType.Local
        }
    }

    private fun showIMG() {
        val currentItem = data[playbackState.currentPosition]
        if (checkNeedDownload(currentItem)) {
            downLoadFile(currentItem)
            return
        }

        with(playbackState) {
            isPlayLocalVideo = false
            if (isPlayPicture && imgUrl != currentItem.content) {
                imgUrl = currentItem.content
                EventBus.getDefault().post(ShowImageEvent(currentItem.content))
            } else if (!isPlayPicture) {
                isPlayPicture = true
                imgUrl = currentItem.content
                EventBus.getDefault().post(ShowImageEvent(currentItem.content))
            }
        }
    }

    private fun showVideo() {
        if (checkNeedDownload(data[playbackState.currentPosition])) {
            downLoadFile(data[playbackState.currentPosition])
        } else {
            EventBus.getDefault().post(ShowVideoEvent())
            playbackState.isPlayLocalVideo = false
            playbackState.isPlayPicture = false
            resolveVideoChange(data[playbackState.currentPosition].content)
            //                    LogHelper.i(MyApp.PLAYER_TAG,"data[currentPosition]: "+data[currentPosition])
        }
    }

    private fun checkNeedDownload(ruleInfo: RuleInfo): Boolean {
        val content = ruleInfo.content
        return content.startsWith("http://") || content.startsWith("https://")
    }

    //不在播放任务时间内，播放本地视频
    private fun playLocalVideo() {
        playbackState.isPlayLocalVideo = true
        playbackState.isPlayPicture = false
//        LogHelper.i(MyApp.PLAYER_TAG,"playLocalVideo,url:"+url)
        EventBus.getDefault().post(ShowVideoEvent())
        resolveVideoChange(url)
    }

    fun startPlay() {
        playbackState.isPlayLocalVideo = true
        playbackState.isPlayPicture = false
        setUp(url, false, "")
        //if (isInPlayingState) {
        //  gsyVideoManager.stop()
        //}
        LogHelper.i(MyApp.PLAYER_TAG, "startPlay")
        isLooping = false
        startPlayLogic()
    }

    private val url: String
        get() = Constants.getConfigDirectory() + "video.mp4"

    @Synchronized
    fun checkRule(list: List<RuleInfo>) {
        val now = Calendar.getInstance()
        val hour = now[Calendar.HOUR_OF_DAY]
        val minute = now[Calendar.MINUTE]
//        LogHelper.i(MyApp.PLAYER_TAG,"时: $hour --- 分:$minute")
        try {
            val currentTask: MutableList<RuleInfo> = ArrayList()
//            LogHelper.i(MyApp.PLAYER_TAG,"添加前currentTask:"+currentTask)
            for (ruleInfo in list) {
                //属于当前时间的加进去，不属于当前时间，应该把它从currentTask中删除
//                LogHelper.i(MyApp.PLAYER_TAG,"ruleInfo:"+ruleInfo)
                if (checkTime(ruleInfo.start_time, hour, ruleInfo.end_time, minute)) {
                    //把时间合适的放到播放任务中
                    currentTask.add(ruleInfo)
                }
            }
//            LogHelper.i(MyApp.PLAYER_TAG,"当前时间播放任务:"+currentTask)
            post {
                if (currentTask.isEmpty()) {
//                    LogHelper.i(MyApp.PLAYER_TAG,"checkRule--isEmpty，播放本地视频")
                    //所有的播放列表都是空的，或者不在播放规则时间内，就播放本地视频
                    playLocalVideo()
//                    val afd = mContext.resources.openRawResourceFd(R.raw.video)
//                    mMediaPlayer.setDataSource(RawDataSourceProvider(afd))
                } else {
                    checkPlayData(currentTask)
                }
            }

        } catch (e: NumberFormatException) {
            LogHelper.e(MyApp.PLAYER_TAG, "error:" + e.message)
//            LogHelper.i(MyApp.PLAYER_TAG,"checkRule--isEmpty")
            //所有的播放列表都是空的，或者不在播放规则时间内，就播放本地视频
            post {
                playLocalVideo()
            }
        }
    }

    /**
     * 每分钟检查一次，在整分钟的时候进行视频切换（如果广告组时间变化，无需等到当前视频播完）
     */
    @Synchronized
    fun checkRule2() {
        val now = Calendar.getInstance()
        val hour = now[Calendar.HOUR_OF_DAY]
        val minute = now[Calendar.MINUTE]
//        LogHelper.i(MyApp.PLAYER_TAG,"时: $hour --- 分:$minute")
        try {
            val currentTask: MutableList<RuleInfo> = ArrayList()
//            LogHelper.i(MyApp.PLAYER_TAG,"添加前currentTask:"+currentTask)
//            LogHelper.i(MyApp.PLAYER_TAG,"原始数据为：${rawData}")
            for (ruleInfo in rawData) {
                //属于当前时间的加进去，不属于当前时间，应该把它从currentTask中删除
//                LogHelper.i(MyApp.PLAYER_TAG,"ruleInfo:"+ruleInfo)
                if (checkTime(ruleInfo.start_time, hour, ruleInfo.end_time, minute)) {
                    //把时间合适的放到播放任务中
                    currentTask.add(ruleInfo)
                }
            }
//            LogHelper.i(MyApp.PLAYER_TAG,"当前时间播放任务:"+currentTask)
            post {
                if (currentTask.isEmpty()) {
                    if (data.size > 0) {
                        data.clear()
                    }
                    if (newAd.size > 0) {
                        newAd.clear()
                    }
                    if (oldAd.size > 0) {
                        oldAd.clear()
                    }
                    if (!playbackState.isPlayLocalVideo) {
//                        LogHelper.i(MyApp.PLAYER_TAG,"checkRule--isEmpty，且当前不是播放本地视频，立即播放本地视频")
                        playLocalVideo()
                    } else {
//                        LogHelper.i(MyApp.PLAYER_TAG,"checkRule--isEmpty，且当前正在播放本地视频，不做操作")
                    }
                } else {
                    checkPlayData2(currentTask)
                }
            }

        } catch (e: NumberFormatException) {
            LogHelper.e(MyApp.PLAYER_TAG, "error: " + e.message)
//            LogHelper.i(MyApp.PLAYER_TAG,"checkRule--isEmpty")
            //所有的播放列表都是空的，或者不在播放规则时间内，就播放本地视频
            post {
                if (!playbackState.isPlayLocalVideo) {
//                    LogHelper.i(MyApp.PLAYER_TAG,"checkRule--isEmpty，且当前不是播放本地视频，立即播放本地视频")
                    playLocalVideo()
                } else {
//                    LogHelper.i(MyApp.PLAYER_TAG,"checkRule--isEmpty，且当前正在播放本地视频，不做操作")
                }
            }
        }
    }

    /**
     * 请求到广告组的时候调用，带入请求到的新广告规则
     */
    @Synchronized
    fun checkRule3(list: List<RuleInfo>) {
        rawData.clear()
        rawData.addAll(list.toMutableList())
//        list.forEach{
//            rawData.add(it.clone() as RuleInfo)
//        }
        LogHelper.i(MyApp.PLAYER_TAG, "原始数据重新赋值为：${rawData}")
        checkRule2()
    }

    private fun checkTime(startTime: String?, hour: Int, endTime: String?, minute: Int): Boolean {
        if (startTime.isNullOrEmpty() || endTime.isNullOrEmpty()) return false

        val (startHour, startMinute) = startTime.split(":").map { it.toInt() }
        val (endHour, endMinute) = endTime.split(":").map { it.toInt() }

        return when {
            startHour < hour && hour < endHour -> true
            startHour == hour && endHour > hour && startMinute <= minute -> true
            hour == endHour && startHour < hour && minute <= endMinute -> true
            hour == startHour && hour == endHour &&
                    minute in startMinute..endMinute -> true

            else -> false
        }
    }

    //比较一下现在的播放任务和上一次的是否相同，如果相同就播放之前的下一个，如果不相同就播放新的任务
    private fun checkPlayData(currentData: MutableList<RuleInfo>) {
//        LogHelper.i(MyApp.PLAYER_TAG,"是否相同:${newAd==oldAd}---newAd:$newAd---oldAd:$oldAd")
//        LogHelper.i(MyApp.PLAYER_TAG,"currentData:"+currentData)
//        if (CollectionUtils.isEqualCollection(data, currentData)) {
        if (newAd == oldAd) {
            if (newAd == null || oldAd == null || data == null) {
//                LogHelper.i(MyApp.PLAYER_TAG,"当前数据异常，与规则不符，播放本地视频")
                playLocalVideo()
            } else {
                //是相同的集合，播放下一个位置
//                LogHelper.i(MyApp.PLAYER_TAG,"checkPlayData，当前视频已播完，是相同的集合，播放下一个位置")
                if (playbackState.currentPosition >= data.size - 1) {
                    playbackState.currentPosition = 0
                } else {
                    playbackState.currentPosition++
                }
            }
        } else {
            //集合内容不同，说明播放的是下一个时间段的
//            LogHelper.i(MyApp.PLAYER_TAG,"checkPlayData，集合内容不同，说明播放的是下一个时间段的")

            playbackState.currentPosition = 0
            data.clear()
            data.addAll(currentData)
        }
//        LogHelper.i(MyApp.PLAYER_TAG,"checkPlayData")
        setPath()
    }

    private fun checkPlayData2(currentData: MutableList<RuleInfo>) {
        newAd.clear()
        for (ruleInfo in currentData) {
            var ruleInfoMap: MutableMap<String, String> = HashMap()
            ruleInfoMap.put("start_time", ruleInfo.start_time)
            ruleInfoMap.put("end_time", ruleInfo.end_time)
            ruleInfoMap.put("content", ruleInfo.content)
            newAd.add(ruleInfoMap)
        }
//        LogHelper.i(MyApp.PLAYER_TAG,"newAd重新赋值为：${newAd}")
        LogHelper.i(MyApp.PLAYER_TAG, "是否相同:${newAd == oldAd}---newAd:$newAd---oldAd:$oldAd")
//        LogHelper.i(MyApp.PLAYER_TAG,"currentData:"+currentData)
//        if (CollectionUtils.isEqualCollection(data, currentData)) {
        if (newAd == oldAd) {
            //是相同的集合，播放下一个位置
            LogHelper.i(MyApp.PLAYER_TAG, "checkPlayData2，当前视频未播完，是相同的集合，不做操作")
//            if (currentPosition >= data.size - 1) {
//                currentPosition = 0
//            } else {
//                currentPosition++
//            }
        } else {
            //集合内容不同，说明播放的是下一个时间段的
//            LogHelper.i(MyApp.PLAYER_TAG,"checkPlayData，集合内容不同，说明播放的是下一个时间段的")

            playbackState.currentPosition = 0
            data.clear()
            currentData.forEach {
                data.add(it.clone() as RuleInfo)
            }
            oldAd.clear()
            oldAd.addAll(newAd)
//            LogHelper.i(MyApp.PLAYER_TAG,"oldAd重新赋值为：${oldAd}")
//            LogHelper.i(MyApp.PLAYER_TAG,"data重新赋值为：${data}")
            setPath()
        }
//        LogHelper.i(MyApp.PLAYER_TAG,"checkPlayData")

    }


    @Synchronized
    private fun downLoadFile(ruleInfo: RuleInfo) {
        //判断当前下载任务是否已经在下载
        if (DownLoadUtil.downloadTask.contains(ruleInfo.content)) {
            LogHelper.i(MyApp.PLAYER_TAG, "当前任务正在下载中")
            //找到上一个已经下载过的视频，如果没有就播放本地视频
            var downloadedIndex: Int = -1
            for (index in 0 until data.size - 1) {
                if (!checkNeedDownload(data[index])) {
                    downloadedIndex = index
                    break
                }
            }
            if (downloadedIndex == -1) {
                LogHelper.i(MyApp.PLAYER_TAG, "没有下载过的视频，播放本地视频")
                playLocalVideo()
            } else {
                LogHelper.i(MyApp.PLAYER_TAG, "找到了已经下载过的视频了，开始播放")
                playbackState.currentPosition = downloadedIndex
                setPath()
            }
        } else {
            DownLoadUtil.downLoadUrl(ruleInfo)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    @Synchronized
    fun onDownloadFinished(event: DownloadFinishEvent) {
        LogHelper.i(MyApp.PLAYER_TAG, "onDownloadFinished")
        //下载视频成功,替换任务列表，中断当前播放任务
        var index = 0
        for (it in data) {
            if (it.dataId == event.ruleInfo.dataId &&
                it.id == event.ruleInfo.id &&
                it.start_time.equals(event.ruleInfo.start_time) &&
                it.end_time.equals(event.ruleInfo.end_time)
            ) {
                index = data.indexOf(it)
                break
            }
        }
        data[index] = event.ruleInfo
        if (index == playbackState.currentPosition || playbackState.isPlayLocalVideo) {
            playbackState.currentPosition = index
            setPath()
        }
        //检查下一个任务，提前下载好
        if (index < data.size - 1) {
            if (checkNeedDownload(data[index + 1])) {
                downLoadFile(data[index + 1])
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    @Synchronized
    fun onDownloadFailed(event: DownloadFailedEvent) {
        LogHelper.e(MyApp.PLAYER_TAG, "onDownloadFailed")
        //下载视频失败，继续任务列表的下一个任务
        nextTask()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReceiveCheckRule(event: CheckTaskEvent) {
        cachedThreadPool.execute {
            synchronized(MyApp.instance) {
                checkRule(ruleDao.allRule())
            }
        }
    }

    /**
     * 开始下一个任务，如果任务列表没有任务，播放本地视频
     */
    private fun nextTask() {
        LogHelper.i(MyApp.PLAYER_TAG, data.toString())
        if (data.isEmpty() || data.size < 2) {
            playLocalVideo()
        } else {
            if (playbackState.currentPosition >= data.size - 1) {
                playbackState.currentPosition = 0
            } else {
                playbackState.currentPosition++
            }
            setPath()
        }
    }

    private fun resolveChangedResult() {
        mTmpManager = null
    }

    private fun releaseTmpManager() {
        if (mTmpManager != null) {
            mTmpManager!!.releaseMediaPlayer()
            mTmpManager = null
        }
    }

    override fun onSurfaceDestroyed(surface: Surface?): Boolean {
        release()
        return super.onSurfaceDestroyed(surface)
    }

    private val gsyMediaPlayerListener: GSYMediaPlayerListener = object : GSYMediaPlayerListener {
        override fun onPrepared() {
            mTmpManager?.let {
                hideAllView()
                it.start()
                val manager: GSYVideoBaseManager = GSYVideoManager.instance()
                GSYVideoManager.changeManager(it)
                it.setLastListener(manager.lastListener())
                it.setListener(manager.listener())
                manager.setDisplay(null)
                if (mSurface == null) {
                    return
                }
                Debuger.printfError("**** showDisplay onSeekComplete ***** $mSurface")
                Debuger.printfError("**** showDisplay onSeekComplete isValid***** " + mSurface.isValid)
//                    LogHelper.i(MyApp.PLAYER_TAG,"mSurface is not null")
                it.setDisplay(mSurface)
                changeUiToPlayingClear()
                resolveChangedResult()

                manager.releaseMediaPlayer()
            }
        }

        override fun onError(what: Int, extra: Int) {
            LogHelper.e(MyApp.PLAYER_TAG, "onError---$what---$extra")
            onVideoReset()
            mTmpManager?.releaseMediaPlayer()
            cachedThreadPool.execute {
                synchronized(MyApp.instance) {
                    checkRule(ruleDao.allRule())
                }
            }
        }

        override fun onAutoCompletion() {}

        override fun onCompletion() {}

        override fun onBufferingUpdate(percent: Int) {}

        override fun onSeekComplete() {}

        override fun onInfo(what: Int, extra: Int) {}

        override fun onVideoSizeChanged() {}

        override fun onBackFullscreen() {}

        override fun onVideoPause() {}

        override fun onVideoResume() {}

        override fun onVideoResume(seek: Boolean) {}

    }
}