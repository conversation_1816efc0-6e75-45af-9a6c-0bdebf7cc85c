package com.stwpower.player.room;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.stwpower.player.bean.RuleInfo;
import com.stwpower.player.constant.Constants;

@Database(entities = RuleInfo.class, version = 1,exportSchema = false)
abstract public class RuleDataBase extends RoomDatabase {
    public abstract RuleDao ruleDao();


    private static RuleDataBase instance;
    public static RuleDataBase getInstance(Context context) {
        if (instance == null){
            instance = Room.databaseBuilder(
                    context.getApplicationContext(),
                    RuleDataBase.class,
                    Constants.DEFAULT_DATABASE_NAME)//数据库名称
                    .build();
        }
        return instance ;
    }
}
