package com.stwpower.player.room

import androidx.room.*
import com.stwpower.player.bean.RuleInfo

@Dao
interface RuleDao {
    @Transaction
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addRule(ruleInfo: RuleInfo)

    @Transaction
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addRule(ruleInfos: List<RuleInfo>)

    @Query("select * from ruleInfo")
    @Transaction
    fun allRule(): List<RuleInfo>

    @Transaction
    @Query("UPDATE ruleInfo SET content= :content WHERE dataId = :dataId")
    fun updateRule(content:String,dataId:Long)

    @Transaction
    @Query("DELETE FROM ruleInfo")
    fun deleteAll()
}