package com.stwpower.player.worker;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageInstaller;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Environment;
import androidx.annotation.NonNull;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import com.blankj.utilcode.util.SPUtils;
import com.stwpower.player.BuildConfig;
import com.stwpower.player.base.MyApp;
import com.stwpower.player.network.MyApiClient;
import com.stwpower.player.receiver.RestartAppReceiver;
import com.stwpower.player.utils.LogHelper;
import com.stwpower.player.utils.SharePreferencesUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class UpdateWorker extends Worker {
    private static final String VERSION_INFO_URL = "http://privacy.stwpower.com/player/player-v4-" + BuildConfig.FLAVOR + "-version.json";
    private static final String APK_FILE_NAME = "player-v4.apk";

    private Context context;

    public UpdateWorker(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
        this.context = context;
    }

    @NonNull
    @Override
    public Result doWork() {
        try {
            // Compare with current version
            PackageManager pm = getApplicationContext().getPackageManager();
            PackageInfo packageInfo = pm.getPackageInfo(getApplicationContext().getPackageName(), 0);
            UUID workId = getId();
            // Check version
            LogHelper.INSTANCE.i(MyApp.PLAYER_TAG,"Starting work: " + workId + "，当前版本为：" + packageInfo.versionCode + "，检查更新");

            String qrCode = SPUtils.getInstance().getString("SNO");
            if(qrCode == null || qrCode.isEmpty()){
                LogHelper.INSTANCE.i(MyApp.PLAYER_TAG,"重新获取qrCode");
                return Result.retry();
            }
            LogHelper.INSTANCE.i(MyApp.PLAYER_TAG,"qrCode: " + qrCode);
            //获取屏幕亮度配置，保存下来
            Map<String, Object> brightnessConfig = MyApiClient.getBrightnessConfig(qrCode);
            if(brightnessConfig != null){
//                Set<String> strings = brightnessConfig.keySet();
//                for (String string : strings) {
//                    LogHelper.INSTANCE.i(MyApp.PLAYER_TAG, "原始数据：" + string + ":" + brightnessConfig.get(string));
//                }
                SharePreferencesUtil.saveToSharePreferences(context,"brightness_config", brightnessConfig);
            }
            Map<String, Object> versionData = MyApiClient.getVersion(qrCode);
            if(versionData != null){
                LogHelper.INSTANCE.i(MyApp.PLAYER_TAG,"needUpdate: " + (boolean)versionData.get("needUpdate"));
                LogHelper.INSTANCE.i(MyApp.PLAYER_TAG,"downloadUrl:" + versionData.get("downloadUrl"));
                LogHelper.INSTANCE.i(MyApp.PLAYER_TAG,"version:" + versionData.get("version"));
            }else{
                LogHelper.INSTANCE.i(MyApp.PLAYER_TAG,"无需升级");
                return Result.success();
            }
            boolean needUpdate = (boolean)versionData.get("needUpdate");
            String downloadUrl = (String)versionData.get("downloadUrl");
            Integer version = Integer.valueOf((String)versionData.get("version"));
            //检查版本
            if (needUpdate && packageInfo.versionCode < version) {
                LogHelper.INSTANCE.i(MyApp.PLAYER_TAG,"需要升级");
            }else{
                LogHelper.INSTANCE.i(MyApp.PLAYER_TAG,"无需升级");
                return Result.success();
            }

            String apkUrl = downloadUrl.replace("FLAVOR", BuildConfig.FLAVOR);
            // Download new version
            OkHttpClient client = new OkHttpClient();
            Request request = new Request.Builder().url(apkUrl).build();
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                LogHelper.INSTANCE.i(MyApp.TERMINAL_TAG,"apk下载地址无效，返回重试");
                return Result.retry();
            }
            LogHelper.INSTANCE.i(MyApp.TERMINAL_TAG,"开始下载");

            File apkFile = new File(getApplicationContext().getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), APK_FILE_NAME);
            InputStream is = response.body().byteStream();
            FileOutputStream fos = new FileOutputStream(apkFile);
            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) != -1) {
                fos.write(buffer, 0, len);
            }
            fos.flush();
            fos.close();
            is.close();
            // 安排重启
//            scheduleRestart(this.getApplicationContext());
            // 静默安装更新的APK
            installSilently(apkFile.getAbsolutePath());

            return Result.success();
        } catch (Exception e) {
            e.printStackTrace();
            LogHelper.INSTANCE.i(MyApp.PLAYER_TAG,"检查版本异常：" + e.getMessage());
            return Result.failure();
        }
    }
    private static void copyFile(File sourceFile, File destFile) throws IOException {
        try (FileInputStream fis = new FileInputStream(sourceFile);
             FileOutputStream fos = new FileOutputStream(destFile)) {

            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) > 0) {
                fos.write(buffer, 0, length);
            }
        }
    }
    private void installSilently(String apkPath) {
        LogHelper.INSTANCE.i(MyApp.TERMINAL_TAG,"开始安装"+Build.VERSION.SDK_INT);
        if(Build.VERSION.SDK_INT < 25){
            try {
                Process proc = Runtime.getRuntime().exec(new String[]{"su", "-c", "pm install -r " + apkPath});
                proc.waitFor();
                if (proc.exitValue() != 0) {
                    InputStream errorStream = proc.getErrorStream();
                    int bytesRead = -1;
                    byte[] buffer = new byte[1024];
                    StringBuilder output = new StringBuilder();
                    while ((bytesRead = errorStream.read(buffer)) > 0) {
                        output.append(new String(buffer, 0, bytesRead));
                    }
                    LogHelper.INSTANCE.e(MyApp.TERMINAL_TAG, "Installation error: " + output.toString());
                    throw new IOException("Non-zero exit value: " + proc.exitValue());
                }
            } catch (Exception e) {
                e.printStackTrace();
                LogHelper.INSTANCE.i(MyApp.PLAYER_TAG,"安装异常：" + e.getMessage());
            }
        }else{
            //安卓7
            LogHelper.INSTANCE.i(MyApp.TERMINAL_TAG, "安卓7开始升级");
            try{
                installApkSilent(apkPath);
            }catch (Exception e){
                LogHelper.INSTANCE.i(MyApp.TERMINAL_TAG,"升级失败");
                e.printStackTrace();
            }

        }
    }

    public boolean installApkSilent(String apkPath) throws IOException {
        //获取PackageInstaller实例
        PackageManager packageManager = context.getPackageManager();
        PackageInstaller packageInstaller = packageManager.getPackageInstaller();
        //创建一个安装会话
        PackageInstaller.SessionParams params = new PackageInstaller.SessionParams(
                PackageInstaller.SessionParams.MODE_FULL_INSTALL);
        int sessionId = packageInstaller.createSession(params);
        PackageInstaller.Session session = packageInstaller.openSession(sessionId);
        //写入APK到会话
        InputStream in = new FileInputStream(apkPath); // apkFile是APK文件的路径
        OutputStream out = session.openWrite("CUST_INSTALL", 0, -1);
        byte[] buffer = new byte[65536];
        int c;
        while ((c = in.read(buffer)) != -1) {
            out.write(buffer, 0, c);
        }
        session.fsync(out);
        in.close();
        out.close();
        //提交会话进行安装
        session.commit(PendingIntent.getBroadcast(context, sessionId,
                new Intent("action_install_complete"), PendingIntent.FLAG_IMMUTABLE).getIntentSender());

        return false;
    }

    private void scheduleRestart(Context context) {
        LogHelper.INSTANCE.i(MyApp.TERMINAL_TAG,"设置重启");
        Intent restartIntent = new Intent(context, RestartAppReceiver.class);
        PendingIntent restartPendingIntent = PendingIntent.getBroadcast(
                context,
                0,
                restartIntent,
                PendingIntent.FLAG_CANCEL_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        long triggerTime = System.currentTimeMillis() + 60 * 1000; // 60秒后

        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        triggerTime,
                        restartPendingIntent
                );
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                alarmManager.setExact(
                        AlarmManager.RTC_WAKEUP,
                        triggerTime,
                        restartPendingIntent
                );
            } else {
                alarmManager.set(
                        AlarmManager.RTC_WAKEUP,
                        triggerTime,
                        restartPendingIntent
                );
            }
        }
    }
}
