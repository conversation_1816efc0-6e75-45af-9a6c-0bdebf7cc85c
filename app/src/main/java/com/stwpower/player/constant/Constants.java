package com.stwpower.player.constant;

import android.os.Environment;
import com.stwpower.player.base.MyApp;

import java.io.File;

/**
 * 作者：ly-xuxiaolong
 * 版本：1.0
 * 创建日期：2020/3/9
 * 描述：
 * 修订历史：
 */
public class Constants{

    public static final String GET_AD = "/cabinet/advertising/get";

    public static final String GET_CONFIG = "/cabinet/advertising/cabinet_advertising";


    public static final String DEFAULT_DATABASE_NAME = "rule2";

    public static final String BUGLY_APPID = "6a25d7c220";

    public static final String GET_LOCATION_ID = "/pos/power/get_location_id";
    public static String getFileDirectory(){
        return (Environment.getExternalStorageDirectory()
                .toString() + File.separator + MyApp.getApp().getApplicationInfo().loadLabel(MyApp.getApp().getPackageManager()) + File.separator + "video"
        );
//        return MyApp.getApp().getFilesDir().getAbsolutePath();
    }
    public static String getConfigDirectory(){
        return (Environment.getExternalStorageDirectory()
                .toString() + File.separator + MyApp.getApp().getApplicationInfo().loadLabel(MyApp.getApp().getPackageManager()) + File.separator);
    }
}
