package com.stwpower.player

import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity
import com.stwpower.player.base.MyApp
import com.stwpower.player.utils.LogHelper
import java.util.*

class HomeActivity : AppCompatActivity() {

    private lateinit var btnPayTerminal: Button
    private lateinit var btnPayApp: Button
    private lateinit var btnLanguageEn: Button
    private lateinit var btnLanguageZh: Button
    private lateinit var btnLanguageDe: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogHelper.i(MyApp.PLAYER_TAG, "HomeActivity onCreate")
        
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        hideSystemUI()
        
        // Set landscape orientation
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        
        setContentView(R.layout.activity_home)
        
        initViews()
        setupClickListeners()
        updateLanguageButtons()
    }

    private fun hideSystemUI() {
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window.setStatusBarColor(Color.TRANSPARENT)
    }

    private fun initViews() {
        btnPayTerminal = findViewById(R.id.btn_pay_terminal)
        btnPayApp = findViewById(R.id.btn_pay_app)
        btnLanguageEn = findViewById(R.id.btn_language_en)
        btnLanguageZh = findViewById(R.id.btn_language_zh)
        btnLanguageDe = findViewById(R.id.btn_language_de)
    }

    private fun setupClickListeners() {
        btnPayTerminal.setOnClickListener {
            LogHelper.i(MyApp.PLAYER_TAG, "Pay with terminal clicked")
            val intent = Intent(this, PayWithTerminalActivity::class.java)
            startActivity(intent)
        }

        btnPayApp.setOnClickListener {
            LogHelper.i(MyApp.PLAYER_TAG, "Pay with app clicked")
            val intent = Intent(this, PayWithAppActivity::class.java)
            startActivity(intent)
        }

        btnLanguageEn.setOnClickListener {
            changeLanguage("en")
        }

        btnLanguageZh.setOnClickListener {
            changeLanguage("zh")
        }

        btnLanguageDe.setOnClickListener {
            changeLanguage("de")
        }
    }

    private fun changeLanguage(languageCode: String) {
        LogHelper.i(MyApp.PLAYER_TAG, "Changing language to: $languageCode")
        
        val locale = Locale(languageCode)
        Locale.setDefault(locale)
        
        val config = resources.configuration
        config.setLocale(locale)
        
        createConfigurationContext(config)
        
        // Update MyApp language setting
        MyApp.setLanguage(languageCode)
        
        // Recreate activity to apply language change
        recreate()
    }

    private fun updateLanguageButtons() {
        // Reset all button backgrounds
        btnLanguageEn.alpha = 0.6f
        btnLanguageZh.alpha = 0.6f
        btnLanguageDe.alpha = 0.6f
        
        // Highlight current language
        when (MyApp.getLanguage()) {
            "en" -> btnLanguageEn.alpha = 1.0f
            "zh" -> btnLanguageZh.alpha = 1.0f
            "de" -> btnLanguageDe.alpha = 1.0f
            else -> btnLanguageEn.alpha = 1.0f // Default to English
        }
    }

    override fun onResume() {
        super.onResume()
        hideSystemUI()
    }

    override fun onBackPressed() {
        // Disable back button or add custom logic
        LogHelper.i(MyApp.PLAYER_TAG, "Back button pressed in HomeActivity")
        // You can add admin verification logic here if needed
    }
}
