package com.stwpower.player

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.content.BroadcastReceiver
import android.content.ComponentCallbacks2
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.ActivityInfo
import android.content.pm.PackageManager
import android.graphics.Color
import android.location.LocationManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import android.view.View
import android.view.Window
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.view.WindowManager
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ImageView
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ToastUtils
import com.bumptech.glide.Glide
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.nayax.emv_core.EmvCore
import com.nayax.emv_core.api.AuthorizationDetails
import com.nayax.emv_core.api.EmvCoreException
import com.nayax.emv_core.api.EmvCoreStatus
import com.nayax.emv_core.api.IEmvCoreClient
import com.nayax.emv_core.api.IPaymentCallbacks
import com.nayax.emv_core.api.ReaderMessage
import com.nayax.emv_core.api.TokenDetails
import com.permissionx.guolindev.PermissionX
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.shuyu.gsyvideoplayer.cache.CacheFactory
import com.shuyu.gsyvideoplayer.player.PlayerFactory
import com.shuyu.gsyvideoplayer.player.SystemPlayerManager
import com.stripe.stripeterminal.Terminal
import com.stripe.stripeterminal.external.callable.BluetoothReaderListener
import com.stripe.stripeterminal.external.callable.Callback
import com.stripe.stripeterminal.external.callable.Cancelable
import com.stripe.stripeterminal.external.callable.DiscoveryListener
import com.stripe.stripeterminal.external.callable.PaymentIntentCallback
import com.stripe.stripeterminal.external.callable.ReaderCallback
import com.stripe.stripeterminal.external.callable.ReaderReconnectionListener
import com.stripe.stripeterminal.external.callable.TerminalListener
import com.stripe.stripeterminal.external.callable.UsbReaderListener
import com.stripe.stripeterminal.external.models.CollectConfiguration
import com.stripe.stripeterminal.external.models.ConnectionConfiguration
import com.stripe.stripeterminal.external.models.ConnectionStatus
import com.stripe.stripeterminal.external.models.DiscoveryConfiguration
import com.stripe.stripeterminal.external.models.PaymentIntent
import com.stripe.stripeterminal.external.models.PaymentStatus
import com.stripe.stripeterminal.external.models.Reader
import com.stripe.stripeterminal.external.models.ReaderSoftwareUpdate
import com.stripe.stripeterminal.external.models.TerminalException
import com.stripe.stripeterminal.log.LogLevel
import com.stwpower.player.base.MyApp
import com.stwpower.player.bean.PopupMessage
import com.stwpower.player.bean.RuleInfo
import com.stwpower.player.bean.VideoAdvertisingInfo
import com.stwpower.player.constant.Constants
import com.stwpower.player.even.CheckTaskEvent
import com.stwpower.player.even.ShowImageEvent
import com.stwpower.player.even.ShowUrlEvent
import com.stwpower.player.even.ShowVideoEvent
import com.stwpower.player.model.MyResponse
import com.stwpower.player.network.MyApiClient
import com.stwpower.player.network.TokenProvider
import com.stwpower.player.player.MyVideoPlayer
import com.stwpower.player.receiver.ReceiveDataReceiver
import com.stwpower.player.room.RuleDao
import com.stwpower.player.room.RuleDataBase
import com.stwpower.player.utils.CustomDialog
import com.stwpower.player.utils.DigestUtil
import com.stwpower.player.utils.LogHelper
import com.stwpower.player.utils.MyUtil
import com.stwpower.player.utils.PathUtil
import com.stwpower.player.utils.SharePreferencesUtil
import com.stwpower.player.utils.getObject
import com.stwpower.player.utils.put
import com.tamsiree.rxfeature.tool.RxQRCode
import de.mindpipe.android.logging.log4j.LogConfigurator
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.log4j.Level
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import tv.danmaku.ijk.media.exo2.Exo2PlayerManager
import tv.danmaku.ijk.media.exo2.ExoPlayerCacheManager
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.concurrent.Executors


class MainActivity :
    AppCompatActivity(),
    BluetoothReaderListener,
    UsbReaderListener,
    DiscoveryListener,
    TerminalListener {

    private var tv_qr_code: AppCompatTextView? = null
    private var iv_qr_code: AppCompatImageView? = null
    private var iv_app_icon: AppCompatImageView? = null
    private var videoPlayer: MyVideoPlayer? = null
    private var image: ImageView? = null
    private var timeReceiver: TimeReceiver? = null
    private var mWebview: WebView? = null
    private var locationId = ""
    private var isDiscover = false

    private val ruleDao: RuleDao by lazy {
        RuleDataBase.getInstance(MyApp.instance.applicationContext).ruleDao()
    }
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions(),
        ::onPermissionResult
    )

    // 创建一个对话框实例
    private lateinit var dialog: CustomDialog

    val cacheExecutor = Executors.newCachedThreadPool()

    private val playerReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action == ReceiveDataReceiver.ACTION_CHARGE_LINK_DATA) {
                val data = intent.getStringExtra(ReceiveDataReceiver.CHARGE_LINK_DATA)
                if (data != null) {
                    var array = data.split(",")
                    if (array.size > 1) {
                        if (array[0] == "400"){
                            //主动请求广告规则
                            val sno = SPUtils.getInstance().getString("SNO", "")
                            if (sno.isEmpty() || sno == null) {
                                getVideoConfig()
                            } else {
                                requestVideo(sno)
                                getLocationId(sno)
                            }
                        }
                        if (array[1] == "6001") {
                            LogHelper.i(MyApp.NAYAX_TAG, "捕获订单，$data")
                            //player,6001,订单号,金额
                            getEmvCoreHandle().ConfirmTransaction(array[2], array[3].toInt(), 1)
                        } else if (array[1] == "6002") {
                            LogHelper.i(MyApp.NAYAX_TAG, "取消订单，$data")
                            //player,6002,订单号
                            getEmvCoreHandle().VoidTransaction(array[2])
                        }
                    }
                }
            }
        }
    }

    private val dialogReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            LogHelper.i(MyApp.PLAYER_TAG, "powerbankid")
            if (intent.action == ReceiveDataReceiver.ACTION_SHOW_DIALOG) {
                val data = intent.getStringExtra(ReceiveDataReceiver.EXTRA_DIALOG_DATA)
                if (data != null) {
                    LogHelper.i(MyApp.PLAYER_TAG, data)
                    //查询该充电宝是否存在订单，是的话，展示订单金额
                    GlobalScope.launch(Dispatchers.IO) {
                        try {
                            var response = MyApiClient.getOrderInfoByPowerBankId(data)
                            if (response.code == 200) {
                                val data = response.data as? Map<String, Any>
                                val usedTime = data?.get("usedTime")
                                val amount = data?.get("amount") as? Double
                                val usedTimeStr = if (usedTime is Number) {
                                    usedTime.toInt().toString()
                                } else {
                                    "N/A"
                                }
                                val amountStr = amount?.let { String.format("%.2f", it) } ?: "N/A"
                                runOnUiThread {
                                    dialog.setMessage(
                                        PopupMessage.return_message.replace(
                                            "TIME",
                                            usedTimeStr
                                        ).replace("MONEY", amountStr)
                                    )
                                    dialog.show()
                                    Handler(Looper.getMainLooper()).postDelayed({
                                        if (dialog.isShowing) {
                                            dialog.dismiss()
                                        }
                                    }, 5000)  // 5000ms = 5s
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        LogHelper.i(MyApp.PLAYER_TAG, "onCreate")
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        hideSystemUI()

        //设置显示方向
        requestedOrientation = if (MyApp.protrait) {
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        } else {
            ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        }

        setContentView(getLayoutResource(MyApp.device_type))
//        BusUtils.register(this)
        EventBus.getDefault().register(this)
        GSYVideoManager.instance().enableRawPlay(applicationContext)
        if (MyApp.device_type.contains("b6") || MyApp.device_type.equals("b12")) {
            //系统内核
            LogHelper.i(MyApp.PLAYER_TAG, "系统内核")
            PlayerFactory.setPlayManager(SystemPlayerManager::class.java)
        } else {
            //EXOPlayer内核
            LogHelper.i(MyApp.PLAYER_TAG, "EXOPlayer内核")
            PlayerFactory.setPlayManager(Exo2PlayerManager::class.java)
            CacheFactory.setCacheManager(ExoPlayerCacheManager::class.java)
        }
        initView()
        runOnUiThread {
            dialog = CustomDialog(this@MainActivity, this@MainActivity, "")
//            dialog.setMessage(PopupMessage.create_order_success)
//            dialog.show()
//
//            Handler(Looper.getMainLooper()).postDelayed({
//                if (dialog.isShowing) {
//                    dialog.dismiss()
//                }
//            }, 30000)  // 5000ms = 5s
        }
        //播放器相关权限
        initData()
        if (MyApp.secretKey != null) {
            when (MyApp.posType) {
                "stripe" -> initStripeTerminal()
                "nayax" -> initNayax()
                else -> LogHelper.i(MyApp.PLAYER_TAG, "无需初始化pos")
            }
        }
        LocalBroadcastManager.getInstance(this).registerReceiver(
            playerReceiver,
            IntentFilter(ReceiveDataReceiver.ACTION_CHARGE_LINK_DATA)
        )
//        onBackPressedDispatcher.addCallback(this, object: OnBackPressedCallback(true) {
//            override fun handleOnBackPressed() {
//                exitOnBackPressed()
//            }
//        })
    }

    private fun hideSystemUI() {
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window.setStatusBarColor(Color.TRANSPARENT)
    }

    /**
     * 退出增加条件
     */
    fun exitOnBackPressed() {
        LogHelper.i(MyApp.PLAYER_TAG,"需要验证管理员权限才能退出")
    }

    private fun getLayoutResource(deviceType: String): Int {
        return when (deviceType) {
            "b6" -> R.layout.play_activity_weishou6
            "b6_2" -> R.layout.play_activity_weishou6_2
            "b6_2_chargez" -> R.layout.play_activity_weishou6_2_chargez
            "b12" -> R.layout.play_activity_weishou12
            "b24" -> R.layout.play_activity_weishou24
            "b24_2" -> R.layout.play_activity_weishou24_2
            "b24_3" -> R.layout.play_activity_weishou24_3
            "b24_4" -> R.layout.play_activity_weishou24_4
            "c12" -> R.layout.play_activity_bajie12
            "c24" -> R.layout.play_activity_bajie24
            "c48" -> R.layout.play_activity_bajie48
            else -> R.layout.play_activity_weishou24
        }
    }

    /**
     * 检查当前设备类型是否支持新的支付UI
     */
    private fun isPaymentUISupported(deviceType: String): Boolean {
        return when (deviceType) {
            // 支持新支付UI的设备类型（横屏设备，适合1080x800显示）
            "b6", "b6_2", "b12", "c12" -> true
            // 特定的横屏设备型号
            "b24_2", "b24_3", "b24_4" -> true
            // 其他设备暂不支持
            "b24", "c24", "c48" -> false
            "b6_2_chargez" -> true  // 特殊充电设备也支持
            else -> false  // 默认不支持
        }
    }

    private fun initStripeTerminal() {
        LogHelper.i(MyApp.TERMINAL_TAG, "初始化stripe terminal")
        requestPermissionsIfNecessary()
        if (
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.BLUETOOTH_CONNECT
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            BluetoothAdapter.getDefaultAdapter()?.let { adapter ->
                if (!adapter.isEnabled) {
                    adapter.enable()
                }
            }
        } else {
            LogHelper.w(MyApp.TERMINAL_TAG, "Failed to acquire Bluetooth permission")
        }
        // 注册充电宝归还广播接收器
        LocalBroadcastManager.getInstance(this).registerReceiver(
            dialogReceiver,
            IntentFilter(ReceiveDataReceiver.ACTION_SHOW_DIALOG)
        )
    }

    private fun initView() {
        videoPlayer = findViewById(R.id.standardGSYVideoPlayer)
        iv_qr_code = findViewById(R.id.iv_qr_code)
        tv_qr_code = findViewById(R.id.tv_qr_code)
        mWebview = findViewById(R.id.webview)
        image = findViewById(R.id.image)

        videoPlayer?.getBackButton()?.visibility = View.GONE
        tv_qr_code?.visibility = View.GONE

        // Add long click listener to QR code area to launch payment pages
        iv_qr_code?.setOnLongClickListener {
            LogHelper.i(MyApp.PLAYER_TAG, "QR code long clicked - checking payment UI support")
            if (isPaymentUISupported(MyApp.device_type)) {
                LogHelper.i(MyApp.PLAYER_TAG, "Device ${MyApp.device_type} supports payment UI - launching payment home")
                launchPaymentHome()
            } else {
                LogHelper.i(MyApp.PLAYER_TAG, "Device ${MyApp.device_type} does not support payment UI")
                // 可以显示一个提示消息，或者什么都不做
                ToastUtils.showShort("Payment UI not supported on this device")
            }
            true
        }
    }

    private fun launchPaymentHome() {
        try {
            val intent = Intent(this, HomeActivity::class.java)
            startActivity(intent)
            LogHelper.i(MyApp.PLAYER_TAG, "Payment home activity launched successfully")
        } catch (e: Exception) {
            LogHelper.e(MyApp.PLAYER_TAG, "Failed to launch payment home: ${e.message}")
        }
    }

    private fun initData() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // 11.0 以上申请权限
            checkManageExternalStoragePermission()
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // 6.0 以上申请权限
            checkPermission()
        } else {
            goNext()
        }
        timeReceiver = TimeReceiver()
        val timeFilter = IntentFilter(Intent.ACTION_TIME_TICK)
        registerReceiver(timeReceiver, timeFilter)
    }

    var REQUEST_CODE = 42

    @RequiresApi(Build.VERSION_CODES.R)
    private fun checkManageExternalStoragePermission() {
        LogHelper.i(MyApp.TERMINAL_TAG, "checkManageExternalStoragePermission")
        if (Environment.isExternalStorageManager()) {
            goNext()
        } else {
            val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
            val uri = Uri.fromParts("package", packageName, null)
            intent.data = uri
            startActivityForResult(intent, REQUEST_CODE)
        }
    }

    @RequiresApi(Build.VERSION_CODES.R)
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == REQUEST_CODE) {
            if (Environment.isExternalStorageManager()) {
                goNext()
            } else {
                // 用户拒绝了权限请求，你可以在这里处理拒绝事件
                LogHelper.e(MyApp.TERMINAL_TAG, "用户拒绝了权限请求")
            }
        }
    }


    private fun checkPermission() {
        PermissionX.init(this).permissions(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_PHONE_STATE
        )
            .request { allGranted: Boolean, grantedList: List<String?>?, deniedList: List<String?>? ->
                if (allGranted) {
                    goNext()
                }
            }
    }


    fun configLogger() {
        val dir = File(logDirectory)
        if (!dir.exists()) {
            try {
                //按照指定的路径创建文件夹
                dir.mkdirs()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        val file = File(logDirectory + "log.txt")
        if (!file.exists()) {
            try {
                //在指定的文件夹中创建文件
                file.createNewFile()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        val logConfigurator = LogConfigurator()
        logConfigurator.fileName = logDirectory + "log.txt"
        logConfigurator.rootLevel = Level.ALL
        logConfigurator.filePattern = "%d %-5p - %m%n"
        logConfigurator.isUseLogCatAppender = false
        logConfigurator.maxFileSize = 1024 * 1024 * 10.toLong()
        logConfigurator.maxBackupSize = 2 // 设置备份文件数量为 2
        logConfigurator.isImmediateFlush = true
        logConfigurator.configure()
    }

    val logDirectory: String
        get() = (Environment.getExternalStorageDirectory()
            .toString() + File.separator + MyApp.getApp().applicationInfo.loadLabel(MyApp.getApp().packageManager) + File.separator + "logs"
                + File.separator)

    val fileDirectory: String
        get() = (Environment.getExternalStorageDirectory()
            .toString() + File.separator + MyApp.getApp().applicationInfo.loadLabel(MyApp.getApp().packageManager) + File.separator + "file"
                + File.separator)

    private fun makeCode() {
        val text = SPUtils.getInstance().getString("QR_CODE")
        tv_qr_code?.let {
            it.visibility = View.VISIBLE
            if (!TextUtils.isEmpty(text)) {
                iv_qr_code?.visibility = View.VISIBLE
                RxQRCode.builder(text)
                    .backColor(-0x1)
                    .codeColor(-0x1000000)
                    .codeSide(600)
                    .codeBorder(1)
                    .into(iv_qr_code)
            }
        }
    }

    private fun checkStoragePermission(): Boolean {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && !Environment.isExternalStorageManager() -> {
                checkManageExternalStoragePermission()
                false
            }

            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED -> {
                checkPermission()
                false
            }

            else -> true
        }
    }

    private fun goNext() {
        if (!checkStoragePermission()) {
            Handler(Looper.getMainLooper()).postDelayed({ goNext() }, 1000)
            return
        }
        configLogger()
        showSno()
        makeCode()

        // 验证本地视频路径
        verifyVideoPaths()

        videoPlayer?.startPlay()
        cacheExecutor.execute {
            synchronized(MyApp.instance) {
                ruleDao.allRule()?.let { rules ->
                    if (rules.isNotEmpty()) {
                        videoPlayer?.checkRule(rules)
                    }
                }
            }
        }

        getVideoConfig()
    }

    private fun verifyVideoPaths() {
        val pathMap = PathUtil.getPathMap()
        val iterator = pathMap.iterator()
        while (iterator.hasNext()) {
            val entry = iterator.next()
            val file = File(entry.value)
            if (!file.exists() || file.length() == 0L) {
                // 文件不存在或大小为0，删除该映射
                iterator.remove()
                LogHelper.w(MyApp.PLAYER_TAG, "发现无效视频路径: ${entry.value}")
            }
        }
        // 保存清理后的映射
        SPUtils.getInstance().put(PathUtil.VIDEO_PATHS, pathMap)
    }

    private fun getVideoConfig() {
        val fno = if (TextUtils.equals(BuildConfig.BUILD_TYPE, "release")) {
            MyUtil.getFNo(this);
        } else {
            "861755075287240"
        }
        LogHelper.i(MyApp.PLAYER_TAG, "获取到的IMEI为: " + fno)
        if (!TextUtils.isEmpty(fno)) {
            GlobalScope.launch(Dispatchers.IO) {
                try {
                    var qrCode = MyApiClient.getQrCode(fno)
                    LogHelper.i(MyApp.PLAYER_TAG, "收到响应:${qrCode}")
                    if (qrCode != null) {
                        SPUtils.getInstance().put("QR_CODE", String.format(MyApp.qrCodeUrl, qrCode))
                        SPUtils.getInstance().put("SNO", qrCode)
                        getLocationId(qrCode)
                        runOnUiThread {
                            makeCode()
                            showSno()
                        }

                        requestVideo(qrCode.toString())
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        } else {
            ToastUtils.showLong("get fno failed")
        }

    }

    private fun getLocationId(qrCode: String) {
        if(MyApp.secretKey != null && MyApp.posType == "stripe"){
            GlobalScope.launch(Dispatchers.IO) {
                try {
                    var result = MyApiClient.getLocationId(qrCode)
                    if (result.code == 200 && result.data != null) {
                        LogHelper.d(MyApp.TERMINAL_TAG, "获取位置id成功，id: " + result.data)
                        SPUtils.getInstance().put("locationId", result.data as String)
                        locationId = result.data as String
                    } else {
                        LogHelper.e(MyApp.TERMINAL_TAG, "获取位置id失败，检查服务端配置")
                    }
                } catch (e: Exception) {
                    LogHelper.e(MyApp.PLAYER_TAG, "" + e.message)
                    e.printStackTrace()
                }
            }
        }
    }

    private fun requestVideo(qrCode: String) {
        GlobalScope.launch(Dispatchers.IO) {
            val timestamp = System.currentTimeMillis() / 1000
            val ip = NetworkUtils.getIPAddress(true)
            val params = "qrCode=${qrCode}&timestamp=${timestamp}&ip=${ip}"
            val sign = DigestUtil.shaEncrypt(params)

            val url =
                MyApp.baseUrl + Constants.GET_AD + "?" + "qrCode=" + qrCode + "&timestamp=" + timestamp + "&ip=" + ip + "&sign=" + sign
            LogHelper.i(MyApp.PLAYER_TAG, "开始发送请求：${url}")
            try {
                var result = MyApiClient.getAD(qrCode, timestamp, ip, sign)
                var videoAdvertisingInfo = convertMyResponseToVideoAdvertisingInfo(result)
                LogHelper.i(MyApp.PLAYER_TAG, "收到响应:${videoAdvertisingInfo.toString()}")
                if (videoAdvertisingInfo.code == 200) {
                    cacheExecutor.execute {
                        synchronized(MyApp.instance) {
                            val oldAd = SPUtils.getInstance()
                                .getObject("task", VideoAdvertisingInfo::class.java)
                            PathUtil.checkChange(videoAdvertisingInfo, oldAd)
                            SPUtils.getInstance().put("task", videoAdvertisingInfo)
                            ruleDao.deleteAll()
                            ruleDao.addRule(videoAdvertisingInfo.data)
                            videoPlayer?.checkRule3(ruleDao.allRule())
                        }
                    }
                }
            } catch (e: Exception) {
                LogHelper.e(MyApp.PLAYER_TAG, "" + e.message)
                e.printStackTrace()
            }
        }
    }

    private fun convertMyResponseToVideoAdvertisingInfo(response: MyResponse): VideoAdvertisingInfo {
        val gson = Gson()

        // 将 data 字段转换为 List<RuleInfo>
        val dataType = object : TypeToken<List<RuleInfo>>() {}.type
        val dataList: List<RuleInfo> = gson.fromJson(gson.toJson(response.data), dataType)

        // 构造 VideoAdvertisingInfo
        return VideoAdvertisingInfo(
            code = response.code ?: -1,
            data = dataList,
            message = response.message ?: "Unknown error"
        )
    }

    private fun showSno() {
        val sno = SPUtils.getInstance().getString("SNO")
        if (tv_qr_code != null && !TextUtils.isEmpty(sno)) {
            tv_qr_code!!.text = sno
        }
    }

    override fun onResume() {
        super.onResume()
        hideSystemUI()
        videoPlayer?.let {
            if (it.visibility == View.VISIBLE) {
                it.onVideoResume()
            }
        }
    }

    override fun onPause() {
        super.onPause()
        videoPlayer?.let {
            if (it.visibility == View.VISIBLE) {
                it.onVideoPause()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReceiveShowImage(event: ShowImageEvent) {
        LogHelper.i(MyApp.PLAYER_TAG, "图片显示开始")
        image?.let {
            it.visibility = View.VISIBLE
            videoPlayer?.visibility = View.INVISIBLE
            mWebview?.visibility = View.INVISIBLE
            Glide.with(this)
                .asBitmap()
                .load(event.url)
                .into(it)
            it.postDelayed({
                //显示图片结束，通知检查任务列表
                LogUtils.d("图片显示结束，开始下一个任务")
                EventBus.getDefault().post(CheckTaskEvent())
            }, 30 * 1000L)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReceiveShowUrl(event: ShowUrlEvent) {
        mWebview?.let {
            if (it.visibility != View.VISIBLE) {
                it.visibility = View.VISIBLE
                videoPlayer?.visibility = View.INVISIBLE
                image?.visibility = View.INVISIBLE
                it.webViewClient = WebViewClient()
                it.loadUrl(event.url)
                it.postDelayed({
                    //显示图片结束，通知检查任务列表
                    LogUtils.d("网页显示结束，开始下一个任务")
                    EventBus.getDefault().post(CheckTaskEvent())
                }, 30 * 1000L)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReceiveShowVideo(event: ShowVideoEvent) {
        image?.visibility = View.GONE
        mWebview?.visibility = View.GONE
        videoPlayer?.visibility = View.VISIBLE
    }


    override fun onDestroy() {
        GSYVideoManager.releaseAllVideos()
//        BusUtils.unregister(this)
        EventBus.getDefault().unregister(this);
        unregisterReceiver(timeReceiver)
        // 注销广播接收器
        LocalBroadcastManager.getInstance(this).unregisterReceiver(dialogReceiver)
        LocalBroadcastManager.getInstance(this).unregisterReceiver(playerReceiver)
        //断开EmvCore服务
        destroy()
        super.onDestroy()
    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        LogHelper.e(MyApp.PLAYER_TAG, "onTrimMemory:$level")
        logMemory()
        if (level == ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN) {
            Glide.get(this).clearMemory()
        }
        Glide.get(this).trimMemory(level)
        GSYVideoManager.instance().clearAllDefaultCache(this)
    }

    override fun onLowMemory() {
        super.onLowMemory()
        LogHelper.e(MyApp.PLAYER_TAG, "onLowMemory")
        logMemory()
        Glide.get(this).clearMemory()
        //清理缓存
        GSYVideoManager.instance().clearAllDefaultCache(this)
    }

    private fun logMemory() {
        //最大分配内存获取方法2
        val maxMemory = (Runtime.getRuntime().maxMemory() * 1.0 / (1024 * 1024)).toFloat()
        //当前分配的总内存
        val totalMemory = (Runtime.getRuntime().totalMemory() * 1.0 / (1024 * 1024)).toFloat()
        //剩余内存
        val freeMemory = (Runtime.getRuntime().freeMemory() * 1.0 / (1024 * 1024)).toFloat()
        LogHelper.i(
            MyApp.PLAYER_TAG,
            "当前应用最大分配内存：$maxMemory---当前分配总内存：$totalMemory---已分配内存中未使用内存:$freeMemory"
        )
    }

    internal inner class TimeReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (TextUtils.equals(Intent.ACTION_TIME_TICK, intent.action)) {
                val calendar = Calendar.getInstance()
                val minute = calendar[Calendar.MINUTE]
                //每隔5分钟获取一次
                if (minute % 5 == 0) {
                    if (Terminal.isInitialized()) {
                        LogHelper.i(
                            MyApp.TERMINAL_TAG,
                            "连接状态：" + Terminal.getInstance().connectionStatus + "；支付状态：" + Terminal.getInstance().paymentStatus
                        )
                    }
                    val sno = SPUtils.getInstance().getString("SNO", "")
                    if (sno.isEmpty() || sno == null) {
                        getVideoConfig()
                    } else {
                        if(BuildConfig.FLAVOR == "RK3288" || BuildConfig.FLAVOR == "SC20"){
                            requestVideo(sno)
                            getLocationId(sno)
                        }
                    }
                }

                //每隔1分钟检查一次规则
                if (minute % 1 == 0) {
                    modifyScreenBrightness()
                    cacheExecutor.execute {
                        synchronized(MyApp.instance) {
                            videoPlayer?.checkRule2()
                        }
                    }
                }
            }
        }
    }

    private fun onPermissionResult(result: Map<String, Boolean>) {
        LogHelper.i(MyApp.TERMINAL_TAG, "权限检查结果")
        val deniedPermissions: List<String> = result
            .filter { !it.value }
            .map { it.key }

        // If we receive a response to our permission check, initialize
        if (deniedPermissions.isEmpty() && !Terminal.isInitialized() && verifyGpsEnabled()) {
            initialize()
        } else {
            LogHelper.i(MyApp.TERMINAL_TAG, "权限未授予，60s后重试")

            Handler(Looper.getMainLooper()).postDelayed({
                requestPermissionsIfNecessary()
            }, 60 * 1000)  // 5000ms = 5s
        }
    }

    private fun initialize() {
        LogHelper.i(MyApp.TERMINAL_TAG, "初始化Terminal")
        // Initialize the Terminal as soon as possible
        try {
            if (isGranted(Manifest.permission.ACCESS_FINE_LOCATION)) {
                Terminal.initTerminal(
                    applicationContext, LogLevel.VERBOSE, TokenProvider(),
                    this
                )
            } else {
                LogHelper.i(
                    MyApp.TERMINAL_TAG,
                    "ACCESS_FINE_LOCATION权限未授予，无法初始化Terminal，60s后重试"
                )
                //60s后重试
                Handler(Looper.getMainLooper()).postDelayed({
                    runOnUiThread {
                        initialize()
                    }
                }, 60 * 1000)
            }

        } catch (e: TerminalException) {
            LogHelper.e(MyApp.TERMINAL_TAG, "初始化Terminal失败,Error:" + e.message)
            throw RuntimeException(
                "Location services are required in order to initialize " +
                        "the Terminal.",
                e
            )
        }
        discoverReadersCheck()
    }

    /**
     * 扫描阅读器检查
     */
    private fun discoverReadersCheck() {
        val sno = SPUtils.getInstance().getString("SNO", "")
        if (!Terminal.isInitialized()) {
            LogHelper.e(MyApp.TERMINAL_TAG, "Terminal未初始化")
            return
        }
        if (sno == null || sno.isEmpty()) {
            LogHelper.e(MyApp.TERMINAL_TAG, "SNO（二维码编号）为空，10s后重试")
            //10s后重试
            Handler(Looper.getMainLooper()).postDelayed({
                discoverReadersCheck()
            }, 10 * 1000)
        } else {
            //本地存储中获取位置id
            locationId = SPUtils.getInstance().getString("locationId", "")
            if (locationId.isEmpty()) {
                //服务端获取
                getLocationId(sno)
            }
            if (locationId.isEmpty()) {
                LogHelper.e(MyApp.TERMINAL_TAG, "该设备的locationId为空，10s重试")
                //10s后重试
                Handler(Looper.getMainLooper()).postDelayed({
                    discoverReadersCheck()
                }, 10 * 1000)
            } else {
                isDiscover = false
                discoverReaders()
            }
        }
    }

    /**
     * 扫描阅读器
     */
    private fun discoverReaders() {
        synchronized(this@MainActivity) {
            if (Terminal.getInstance().connectionStatus != ConnectionStatus.CONNECTED && !isDiscover) {
                isDiscover = true
                LogHelper.i(
                    MyApp.TERMINAL_TAG,
                    "非连接状态且非扫描状态，开始扫描阅读器，位置id：$locationId"
                )
                // 2. 搜索阅读器
                Terminal
                    .getInstance()
                    .discoverReaders(
                        config = DiscoveryConfiguration(
                            0,
                            MyApp.discoveryMethod,
                            MyApp.isSimulated,
                            locationId
                        ),
                        this,
                        callback = object : Callback {
                            override fun onSuccess() {
                                LogHelper.i(MyApp.TERMINAL_TAG, "扫描阅读器成功")
                                isDiscover = false
                            }

                            override fun onFailure(e: TerminalException) {
                                //You must disconnect from reader before discovering readers.
                                isDiscover = false
                                LogHelper.e(
                                    MyApp.TERMINAL_TAG,
                                    "扫描阅读器失败，重新扫描" + e.message
                                )
                                //30s后重试
                                Handler(Looper.getMainLooper()).postDelayed({
                                    runOnUiThread {
                                        try {
                                            if (Terminal.getInstance().connectionStatus == ConnectionStatus.CONNECTED) {
                                                LogHelper.i(MyApp.TERMINAL_TAG, "断开连接")
                                                Terminal.getInstance()
                                                    .disconnectReader(disconnectReaderCallback)
                                            } else {
                                                LogHelper.i(
                                                    MyApp.TERMINAL_TAG,
                                                    "不是已连接状态，无法断开，重新扫描。连接状态：" + Terminal.getInstance().connectionStatus + "，支付状态：" + Terminal.getInstance().paymentStatus
                                                )
                                                discoverReaders()
                                            }
                                        } catch (e: TerminalException) {
                                            LogHelper.e(
                                                MyApp.TERMINAL_TAG,
                                                "断开阅读器异常：" + e.message
                                            )
                                            discoverReaders()
                                        }
                                    }
                                }, 30 * 1000)
                            }
                        }
                    )
            } else {
                LogHelper.d(
                    MyApp.TERMINAL_TAG,
                    "不进入扫描，连接状态为：" + Terminal.getInstance().connectionStatus + "，扫描状态为：" + isDiscover
                )
            }
        }
    }

    private fun verifyGpsEnabled(): Boolean {
        val locationManager: LocationManager? =
            applicationContext.getSystemService(Context.LOCATION_SERVICE) as LocationManager?
        var gpsEnabled = false

        try {
            gpsEnabled = locationManager?.isProviderEnabled(LocationManager.GPS_PROVIDER) ?: false
        } catch (_: Exception) {
        }

        if (!gpsEnabled) {
            // notify user
            AlertDialog.Builder(
                android.view.ContextThemeWrapper(
                    this,
                    com.google.android.material.R.style.Theme_MaterialComponents_DayNight_DarkActionBar
                )
            )
                .setMessage("Please enable location services")
                .setCancelable(false)
                .setPositiveButton("Open location settings") { _, _ ->
                    this.startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
                }
                .create()
                .show()
        }

        return gpsEnabled
    }

    private fun requestPermissionsIfNecessary() {
        if (Build.VERSION.SDK_INT >= 31) {
            LogHelper.i(MyApp.TERMINAL_TAG, "权限检查（SDK>=31）")
            requestPermissionsIfNecessarySdk31()
        } else {
            LogHelper.i(MyApp.TERMINAL_TAG, "权限检查（SDK<31）")
            requestPermissionsIfNecessarySdkBelow31()
        }
    }

    private fun isGranted(permission: String): Boolean {
        return ContextCompat.checkSelfPermission(
            this,
            permission
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun requestPermissionsIfNecessarySdkBelow31() {
        // Check for location permissions
        if (!isGranted(Manifest.permission.ACCESS_FINE_LOCATION)) {
            // If we don't have them yet, request them before doing anything else
            requestPermissionLauncher.launch(arrayOf(Manifest.permission.ACCESS_FINE_LOCATION))
        } else if (!Terminal.isInitialized() && verifyGpsEnabled()) {
            initialize()
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun requestPermissionsIfNecessarySdk31() {
        // Check for location and bluetooth permissions
        val deniedPermissions = mutableListOf<String>().apply {
            if (!isGranted(Manifest.permission.ACCESS_FINE_LOCATION)) add(Manifest.permission.ACCESS_FINE_LOCATION)
            if (!isGranted(Manifest.permission.BLUETOOTH_CONNECT)) add(Manifest.permission.BLUETOOTH_CONNECT)
            if (!isGranted(Manifest.permission.BLUETOOTH_SCAN)) add(Manifest.permission.BLUETOOTH_SCAN)
        }.toTypedArray()

        if (deniedPermissions.isNotEmpty()) {
            // If we don't have them yet, request them before doing anything else
            requestPermissionLauncher.launch(deniedPermissions)
        } else if (!Terminal.isInitialized() && verifyGpsEnabled()) {
            initialize()
        }
    }

    override fun onUpdateDiscoveredReaders(readers: List<Reader>) {
        isDiscover = false
        LogHelper.i(MyApp.TERMINAL_TAG, "扫描到设备数量：" + readers.size + "，进行连接")
//        locationId = "tml_FWyp3wF6Eup0mQ"
        try {
            Terminal.getInstance().connectUsbReader(
                readers[0],
                ConnectionConfiguration.UsbConnectionConfiguration(
                    locationId,
                    false,
                    usbReaderReconnectionListener
                ),
                this,
                connectUsbReaderCallback
            )
        } catch (e: Exception) {
            //You must disconnect from reader before starting a new connection.
            LogHelper.i(MyApp.TERMINAL_TAG, "连接阅读器失败，10s后重新扫描：" + e.message)
            runOnUiThread {
                dialog.setMessage(e.message!!)
                dialog.show()

                Handler(Looper.getMainLooper()).postDelayed({
                    if (dialog.isShowing) {
                        dialog.dismiss()
                    }
                }, 5000)  // 5000ms = 5s
            }
            Handler(Looper.getMainLooper()).postDelayed({
                discoverReaders()
            }, 10 * 1000)
        }

    }

    override fun onUnexpectedReaderDisconnect(reader: Reader) {
        LogHelper.i(MyApp.TERMINAL_TAG, "阅读器意外断开连接")
    }

    override fun onConnectionStatusChange(status: ConnectionStatus) {
        LogHelper.i(MyApp.TERMINAL_TAG, "阅读器连接状态改变，状态为：" + status)
        //NOT_CONNECTED 未连接
        //CONNECTING 连接中
        //CONNECTED 已连接（正常工作，等待刷卡时应处于该状态）

        if (status == ConnectionStatus.NOT_CONNECTED) {
            Handler(Looper.getMainLooper()).postDelayed({
                if (Terminal.getInstance().connectionStatus == ConnectionStatus.NOT_CONNECTED) {
                    LogHelper.i(MyApp.TERMINAL_TAG, "阅读器未连接，超过30s处于未连接，重新扫描")
                    discoverReaders()
                }
            }, 30 * 1000)
        }

        if (status == ConnectionStatus.CONNECTING) {
            Handler(Looper.getMainLooper()).postDelayed({
                if (Terminal.getInstance().connectionStatus == ConnectionStatus.CONNECTING) {
                    LogHelper.i(MyApp.TERMINAL_TAG, "阅读器连接中，超过20s处于连接中，进行升级")
                    try {
                        Terminal.getInstance().installAvailableUpdate()
                    } catch (e: TerminalException) {
                        LogHelper.e(MyApp.TERMINAL_TAG, "升级阅读器失败: " + e.message)
                    }
                }
            }, 20 * 1000)
        }
    }

    override fun onPaymentStatusChange(status: PaymentStatus) {
        LogHelper.i(MyApp.TERMINAL_TAG, "支付状态改变，状态为：" + status)
        //NOT_READY 未准备好
        //READY 准备好（状态为CONNECTED时，未进入收集付款信息时，处于该状态）
        //WAITING_FOR_INPUT 等待输入（等待刷卡时处于该状态）
        //PROCESSING 正在处理

        //如果处于NOT_READY，10s后还是处于NOT_READY，进行重连
        if (status == PaymentStatus.NOT_READY) {
            Handler(Looper.getMainLooper()).postDelayed({
                if (Terminal.getInstance().paymentStatus == PaymentStatus.NOT_READY) {
                    LogHelper.i(MyApp.TERMINAL_TAG, "支付状态超过30s处于NOT_READY，重新扫描")
                    discoverReaders()
                }
            }, 30 * 1000)
        }
        //如果处于READY，10s后还是处于READY，进入收集付款方式
        if (status == PaymentStatus.READY) {
            Handler(Looper.getMainLooper()).postDelayed({
                if (Terminal.getInstance().paymentStatus == PaymentStatus.READY) {
                    LogHelper.i(MyApp.TERMINAL_TAG, "支付状态超过10s处于READY，进入收集付款方式")
                    collectPaymentMethod()
                    checkPaymentStatus()
                }
            }, 10 * 1000)
        }
    }

    private fun checkPaymentStatus() {
        LogHelper.i(MyApp.TERMINAL_TAG, "检查支付状态:${Terminal.getInstance().paymentStatus}")

        lifecycleScope.launch {
            LogHelper.i(
                MyApp.TERMINAL_TAG,
                "是否为READY：${Terminal.getInstance().paymentStatus == PaymentStatus.READY}"
            )
            while (Terminal.getInstance().paymentStatus == PaymentStatus.READY) {
                delay(30 * 1000)  // 延迟30秒
                if (Terminal.getInstance().paymentStatus == PaymentStatus.READY) {
                    LogHelper.i(MyApp.TERMINAL_TAG, "支付状态超过30s任处于READY，进入收集付款方式")
                    collectPaymentMethod()
                }
            }
        }
    }

    override fun onStartInstallingUpdate(update: ReaderSoftwareUpdate, cancelable: Cancelable?) {
        LogHelper.i(MyApp.TERMINAL_TAG, "开始升级阅读器")
        runOnUiThread {
            dialog.setMessage(PopupMessage.upgrading_start)
            dialog.show()
        }
    }

    override fun onReportReaderSoftwareUpdateProgress(progress: Float) {
        LogHelper.i(
            MyApp.TERMINAL_TAG,
            "升级阅读器进度：" + String.format("%.2f", progress * 100) + "%"
        )
        runOnUiThread {
            dialog.setMessage(
                PopupMessage.upgrading + " " + String.format(
                    "%.2f",
                    progress * 100
                ) + "%"
            )
            dialog.show()
        }
    }

    override fun onFinishInstallingUpdate(update: ReaderSoftwareUpdate?, e: TerminalException?) {
        LogHelper.i(MyApp.TERMINAL_TAG, "升级阅读器结束")
        Handler(Looper.getMainLooper()).postDelayed({
            if (dialog.isShowing) {
                dialog.dismiss()
            }
        }, 3000)
    }

    private val disconnectReaderCallback = object : Callback {
        override fun onFailure(e: TerminalException) {
            LogHelper.i(MyApp.TERMINAL_TAG, "断开阅读器失败: " + e.message)
        }

        override fun onSuccess() {
            LogHelper.i(MyApp.TERMINAL_TAG, "断开阅读器成功")
            Terminal.getInstance().clearCachedCredentials()
            discoverReaders()
        }
    }

    private val usbReaderReconnectionListener = object : ReaderReconnectionListener {
        override fun onReaderReconnectFailed(reader: Reader) {
            LogHelper.i(MyApp.TERMINAL_TAG, "阅读器重连失败")
        }

        override fun onReaderReconnectStarted(reader: Reader, cancelReconnect: Cancelable) {
            LogHelper.i(MyApp.TERMINAL_TAG, "阅读器开始重连")
        }

        override fun onReaderReconnectSucceeded(reader: Reader) {
            LogHelper.i(MyApp.TERMINAL_TAG, "阅读器重连成功（不进入收集付款方式，由状态改变统一管理）")
        }

    }
    private val connectUsbReaderCallback = object : ReaderCallback {
        override fun onSuccess(reader: Reader) {
            LogHelper.i(
                MyApp.TERMINAL_TAG,
                "连接阅读器成功，进行下一步，（升级）创建付款意图，进入等待刷卡状态"
            )
            //弹窗
            runOnUiThread {
                dialog.setMessage(PopupMessage.connect_success)
                dialog.show()

                Handler(Looper.getMainLooper()).postDelayed({
                    if (dialog.isShowing) {
                        dialog.dismiss()
                    }
                }, 3000)  // 5000ms = 5s
            }
            collectPaymentMethod()
        }

        override fun onFailure(e: TerminalException) {
            //You must disconnect from reader before starting a new connection.
            LogHelper.e(MyApp.TERMINAL_TAG, "连接阅读器失败，10s后重新扫描: " + e.message)
            runOnUiThread {
                dialog.setMessage(e.message!!)
                dialog.show()

                Handler(Looper.getMainLooper()).postDelayed({
                    if (dialog.isShowing) {
                        dialog.dismiss()
                    }
                }, 5000)  // 5000ms = 5s
            }
            Handler(Looper.getMainLooper()).postDelayed({
                discoverReaders()
            }, 10 * 1000)
        }
    }

    private var collectPaymentMethod: Cancelable? = null
    private val handler = Handler(Looper.getMainLooper())
    private val cancelCollectPaymentRunnable = Runnable {
        runOnUiThread {
            try {
                LogHelper.i(MyApp.TERMINAL_TAG, "超过30分钟，取消收集付款方式")
                collectPaymentMethod!!.cancel(cancelCollectPaymentMethodCallback)
            } catch (e: TerminalException) {
                LogHelper.e(MyApp.TERMINAL_TAG, "取消收集付款方式失败：" + e.message)
                discoverReaders()
            }
        }
    }

    /**
     * 收集付款方式
     */
    private fun collectPaymentMethod() {
        CoroutineScope(Dispatchers.IO).launch {
            // 创建PaymentIntent
            try {
                val sno = SPUtils.getInstance().getString("SNO", "")
                val result = MyApiClient.createPaymentIntent(sno)
                withContext(Dispatchers.Main) {
                    if (result != null && result["clientSecret"] != null) {
                        LogHelper.i(
                            MyApp.TERMINAL_TAG,
                            "服务端创建PaymentIntent成功，取回PaymentIntent"
                        )
                        //正常连接时：CONNECTED
                        //拔掉USB线时：CONNECTED（异常，什么事件也没有触发，过一会触发重连，居然能连接成功）
                        //接回USB线，触发重连
                        //黑色这款，会出现读卡成功后，一直卡在授权，然后提示Thank you，没有触发回调，屏幕回到刷卡提示
                        //黑色这款，1个小时后超时，没有触发任何回调，两次超时后，直接回到"stripe"页面，无法进行刷卡，检查状态也仍然是WAITING_FOR_INPUT，故而增加30分钟主动取消收集付款方式
                        Terminal.getInstance().retrievePaymentIntent(
                            result["clientSecret"]!!,
                            object : PaymentIntentCallback {
                                override fun onSuccess(paymentIntent: PaymentIntent) {
                                    val collectConfig = CollectConfiguration.Builder()
                                        .skipTipping(false)
                                        .build()
                                    synchronized(this@MainActivity) {
                                        if (Terminal.getInstance().connectionStatus != ConnectionStatus.CONNECTED) {
                                            LogHelper.d(MyApp.TERMINAL_TAG, "阅读器未连接")
                                        } else if (Terminal.getInstance().paymentStatus == PaymentStatus.WAITING_FOR_INPUT) {
                                            LogHelper.d(MyApp.TERMINAL_TAG, "已处于收集付款状态")
                                        } else {
                                            //收集付款方式
                                            LogHelper.i(
                                                MyApp.TERMINAL_TAG,
                                                "取回PaymentIntent成功，paymentIntent: $paymentIntent ，开始收集付款方式"
                                            )
                                            collectPaymentMethod =
                                                Terminal.getInstance().collectPaymentMethod(
                                                    paymentIntent,
                                                    collectPaymentMethodCallback,
                                                    collectConfig
                                                )
                                            handler.postDelayed(
                                                cancelCollectPaymentRunnable,
                                                30 * 60 * 1000
                                            )
                                        }
                                    }
                                }

                                override fun onFailure(exception: TerminalException) {
                                    LogHelper.e(
                                        MyApp.TERMINAL_TAG,
                                        "取回PaymentIntent失败: " + exception.message
                                    )
                                }
                            })
                    }
                }
            } catch (e: java.lang.Exception) {
                LogHelper.e(MyApp.TERMINAL_TAG, "进入收集付款方式异常：${e.message}")
            }
        }
    }

    /**
     * 收集付款方式回调（确认付款）
     */
    private val collectPaymentMethodCallback = object : PaymentIntentCallback {
        override fun onSuccess(paymentIntent: PaymentIntent) {
            //取消定时任务
            LogHelper.i(MyApp.TERMINAL_TAG, "收集付款方式成功，取消定时任务")
            handler.removeCallbacks(cancelCollectPaymentRunnable)
            //3. 确认付款（确认成功就调用租借接口）
            LogHelper.i(MyApp.TERMINAL_TAG, "收集付款方式成功，开始确认付款")
            //弹窗
            runOnUiThread {
                dialog.setMessage(PopupMessage.collection_success)
                dialog.show()

                Handler(Looper.getMainLooper()).postDelayed({
                    if (dialog.isShowing) {
                        dialog.dismiss()
                    }
                }, 1000)  // 5000ms = 5s
            }
            Terminal.getInstance().processPayment(paymentIntent, processPaymentCallback)
        }

        override fun onFailure(e: TerminalException) {
            //取消定时任务
            LogHelper.i(MyApp.TERMINAL_TAG, "收集付款方式失败，取消定时任务")
            handler.removeCallbacks(cancelCollectPaymentRunnable)
            LogHelper.e(MyApp.TERMINAL_TAG, "收集付款方式失败，重新收集" + e.message)
            //弹窗
            runOnUiThread {
                dialog.setMessage(PopupMessage.collection_fail)
                dialog.show()

                Handler(Looper.getMainLooper()).postDelayed({
                    if (dialog.isShowing) {
                        dialog.dismiss()
                    }
                }, 3000)  // 5000ms = 5s
            }
            collectPaymentMethod()
        }
    }

    /**
     * 确认付款回调（租借）
     */
    private val processPaymentCallback = object : PaymentIntentCallback {
        override fun onSuccess(paymentIntent: PaymentIntent) {
            //确认成功，开始租借
            LogHelper.i(MyApp.TERMINAL_TAG, "确认付款成功，开始租借")
            val map: MutableMap<String, String> = HashMap()
            map["paymentIntentId"] = paymentIntent.id
            map["appSecretKey"] = MyApp.secretKey
            val sno = SPUtils.getInstance().getString("SNO", "")
            map["qrCode"] = sno

            try {
                val orderNo = MyApiClient.lendPowerStripeTerminal(map)
                if (orderNo != null) {
                    LogHelper.i(MyApp.TERMINAL_TAG, "租借成功，$orderNo")
                    //弹窗
                    runOnUiThread {
                        dialog.setMessage(PopupMessage.create_order_success)
                        dialog.show()

                        Handler(Looper.getMainLooper()).postDelayed({
                            if (dialog.isShowing) {
                                dialog.dismiss()
                            }
                        }, 20000)  // 5000ms = 5s
                    }
                } else {
                    LogHelper.e(MyApp.TERMINAL_TAG, "租借失败")
                    // 弹窗
                    runOnUiThread {
                        dialog.setMessage(PopupMessage.create_order_fail)
                        dialog.show()

                        Handler(Looper.getMainLooper()).postDelayed({
                            if (dialog.isShowing) {
                                dialog.dismiss()
                            }
                        }, 10000)  // 5000ms = 5s
                    }
                }
                LogHelper.i(MyApp.TERMINAL_TAG, "重新回到等待用户刷卡状态")
                collectPaymentMethod()
            } catch (e: IOException) {
                throw java.lang.RuntimeException(e)
            }
        }

        override fun onFailure(e: TerminalException) {
            //确认失败，换卡重试
            LogHelper.e(
                MyApp.TERMINAL_TAG,
                "确认付款失败，重新收集付款方式，回到等待用户刷卡状态，errorCode:" + e.errorCode + ", errorMessage:" + e.message
            )
            // 弹窗
            runOnUiThread {
                dialog.setMessage(e.message!!)
                dialog.show()

                Handler(Looper.getMainLooper()).postDelayed({
                    if (dialog.isShowing) {
                        dialog.dismiss()
                    }
                }, 10000)  // 5000ms = 5s
            }
            collectPaymentMethod()
        }
    }

    /**
     * 取消收集付款方式回调
     */
    private val cancelCollectPaymentMethodCallback = object : Callback {
        override fun onFailure(e: TerminalException) {
            LogHelper.i(MyApp.TERMINAL_TAG, "取消付款意图失败: " + e.message)
        }

        override fun onSuccess() {
            LogHelper.i(MyApp.TERMINAL_TAG, "取消付款意图成功")
        }
    }

    // nayax
    private var firstTime = true
    private var readyCount = 0
    private var lastStatus: EmvCoreStatus? = null
    var _emvCore: EmvCore? = null

    private fun initNayax() {
        LogHelper.i(MyApp.NAYAX_TAG, "初始化nayax")
        // 注册充电宝归还广播接收器
        LocalBroadcastManager.getInstance(this).registerReceiver(
            dialogReceiver,
            IntentFilter(ReceiveDataReceiver.ACTION_SHOW_DIALOG)
        )
        getEmvCoreHandle().SetManagementCallbacks { status: EmvCoreStatus? ->
            when (status) {
                EmvCoreStatus.NotReady -> {
                    readyCount = 0
                    lastStatus = EmvCoreStatus.NotReady
                    LogHelper.i(MyApp.NAYAX_TAG, "Not Ready")
                }

                EmvCoreStatus.Ready -> {
                    if (lastStatus == EmvCoreStatus.Ready) {
                        readyCount++
                    }
                    if (readyCount > 3) {
                        firstTime = true
                    }
                    lastStatus = EmvCoreStatus.Ready
                    LogHelper.i(MyApp.NAYAX_TAG, "Ready")
                    //进入授权模式
                    if (firstTime) {
                        preAuth()
                        firstTime = false
                    }
                }

                EmvCoreStatus.PaymentTransaction -> {
                    readyCount = 0
                    lastStatus = EmvCoreStatus.PaymentTransaction
                    LogHelper.i(MyApp.NAYAX_TAG, "PaymentTransaction")
                }

                EmvCoreStatus.Update -> {
                    readyCount = 0
                    lastStatus = EmvCoreStatus.Update
                    LogHelper.i(MyApp.NAYAX_TAG, "Update")
                }

                EmvCoreStatus.NoReader -> {
                    readyCount = 0
                    lastStatus = EmvCoreStatus.NoReader
                    LogHelper.i(MyApp.NAYAX_TAG, "NoReader")
                }

                EmvCoreStatus.NoTerminalId -> {
                    readyCount = 0
                    lastStatus = EmvCoreStatus.NoTerminalId
                    LogHelper.i(MyApp.NAYAX_TAG, "NoTerminalId")
                }

                else -> {
                    LogHelper.i(MyApp.NAYAX_TAG, "else")
                }
            }
        }

        LogHelper.i(MyApp.NAYAX_TAG, "Initialize")
        // connect to EMV Core service
        getEmvCoreHandle().Initialize(this)
    }

    fun getEmvCoreHandle(): IEmvCoreClient {
        return _emvCore ?: EmvCore().also { _emvCore = it }
    }

    private fun preAuth() {
        synchronized(getEmvCoreHandle()) {
            try {
                setPaymentCallbacks()
                try {
                    LogHelper.i(MyApp.NAYAX_TAG, "从服务端获取预授权金额")
                    val map =
                        MyApiClient.getPreAmount()
                    val amount =
                        Math.round(map["amount"]!!.toDouble()).toInt()
                    val current = map["current"]!!.toInt()
                    LogHelper.i(MyApp.NAYAX_TAG, "获取到的金额：$amount, 单位：$current")
                    getEmvCoreHandle().PreAuthorize(amount, current, 0, 60, true)
                } catch (e: IOException) {
                    LogHelper.e(MyApp.NAYAX_TAG, e.message!!)
                }
            } catch (e: EmvCoreException) {
                LogHelper.e(MyApp.NAYAX_TAG, e.message!!)
            }
        }
    }

    fun destroy() {
        if(MyApp.secretKey != null && MyApp.posType != null && MyApp.posType == "nayax"){
            getEmvCoreHandle().Destroy()
        }
    }

    private fun dismiss(delayMillis: Int) {
        handler.postDelayed(Runnable { dialog.dismiss() }, delayMillis.toLong())
    }

    private fun setPaymentCallbacks() {
        getEmvCoreHandle().SetPaymentCallbacks(object : IPaymentCallbacks {
            override fun TransactionComplete(authorizationDetails: AuthorizationDetails) {
                if (authorizationDetails == null) {
                    LogHelper.i(MyApp.NAYAX_TAG, "authorizationDetails is null")
                    return
                }
                if (authorizationDetails.Status != null && "OK" == authorizationDetails.Status) {
                    LogHelper.i(
                        MyApp.NAYAX_TAG,
                        """
                        ErrorDescription: ${authorizationDetails.ErrorDescription}
                        ErrorCode: ${authorizationDetails.ErrorCode}
                        Status: ${authorizationDetails.Status}
                        AmountAuthorized: ${authorizationDetails.AmountAuthorized}
                        AmountRequested: ${authorizationDetails.AmountRequested}
                        AuthorizationCode: ${authorizationDetails.AuthorizationCode}
                        PartialPan: ${authorizationDetails.PartialPan}
                        CardType: ${authorizationDetails.CardType}
                        AuthID: ${authorizationDetails.AuthID}
                        ReceiptID: ${authorizationDetails.ReceiptID}
                        Channel: ${authorizationDetails.Channel}
                        AID: ${authorizationDetails.AID}
                        TVR: ${authorizationDetails.TVR}
                        IAD: ${authorizationDetails.IAD}
                        TSI: ${authorizationDetails.TSI}
                        ARC: ${authorizationDetails.ARC}
                        TransactionTime: ${authorizationDetails.TransactionTime}
                        Application_Label: ${authorizationDetails.Application_Label}
                        Currency: ${authorizationDetails.Currency}
                        CVM: ${authorizationDetails.CVM}
                        IsTransactionApproved: ${authorizationDetails.IsTransactionApproved}
                        CardToken: ${authorizationDetails.CardToken}
                        Additional_Parameters: ${authorizationDetails.Additional_Parameters}
                        Transaction_Reference: ${authorizationDetails.Transaction_Reference}
                        RRN: ${authorizationDetails.RRN}
                        EntryMode: ${authorizationDetails.EntryMode}
                        Card_ID: ${authorizationDetails.Card_ID}
                        Card_Balance: ${authorizationDetails.Card_Balance}
                        """.trimIndent()
                    )
                } else {
                    LogHelper.i(
                        MyApp.NAYAX_TAG, """
                         无效数据
                         ErrorDescription: ${authorizationDetails.ErrorDescription}
                         ErrorCode: ${authorizationDetails.ErrorCode}
                         Status: ${authorizationDetails.Status}
                         AmountAuthorized: ${authorizationDetails.AmountAuthorized}
                         AmountRequested: ${authorizationDetails.AmountRequested}
                         AuthorizationCode: ${authorizationDetails.AuthorizationCode}
                         PartialPan: ${authorizationDetails.PartialPan}
                         CardType: ${authorizationDetails.CardType}
                         AuthID: ${authorizationDetails.AuthID}
                         ReceiptID: ${authorizationDetails.ReceiptID}
                         Channel: ${authorizationDetails.Channel}
                         AID: ${authorizationDetails.AID}
                         TVR: ${authorizationDetails.TVR}
                         IAD: ${authorizationDetails.IAD}
                         TSI: ${authorizationDetails.TSI}
                         ARC: ${authorizationDetails.ARC}
                         TransactionTime: ${authorizationDetails.TransactionTime}
                         Application_Label: ${authorizationDetails.Application_Label}
                         Currency: ${authorizationDetails.Currency}
                         CVM: ${authorizationDetails.CVM}
                         IsTransactionApproved: ${authorizationDetails.IsTransactionApproved}
                         CardToken: ${authorizationDetails.CardToken}
                         Additional_Parameters: ${authorizationDetails.Additional_Parameters}
                         Transaction_Reference: ${authorizationDetails.Transaction_Reference}
                         RRN: ${authorizationDetails.RRN}
                         EntryMode: ${authorizationDetails.EntryMode}
                         Card_ID: ${authorizationDetails.Card_ID}
                         Card_Balance: ${authorizationDetails.Card_Balance}
                         """.trimIndent()
                    )
                }
                if (authorizationDetails.ErrorCode == 0 && authorizationDetails.Status == "OK" && authorizationDetails.IsTransactionApproved == true &&
                    (authorizationDetails.Transaction_Reference != null && authorizationDetails.Transaction_Reference.length > 0) &&
                    (authorizationDetails.PartialPan != null && authorizationDetails.PartialPan.length > 0)
                ) {
                    LogHelper.i(
                        MyApp.NAYAX_TAG,
                        "预授权成功，进行弹宝，Transaction_Reference：" + authorizationDetails.Transaction_Reference
                    )
                    try {
                        val body: MutableMap<String, Any> = java.util.HashMap()
                        val sno = SPUtils.getInstance().getString("SNO", "")
                        body["qrCode"] = sno
                        body["partialPan"] = authorizationDetails.PartialPan
                        body["reference"] = authorizationDetails.Transaction_Reference
                        body["amountAuthorized"] = authorizationDetails.AmountAuthorized
                        body["appSecretKey"] = MyApp.secretKey
                        val orderNo = MyApiClient.lendPowerNayax(body)
                        if (orderNo != null) {
                            LogHelper.i(MyApp.NAYAX_TAG, "订单创建成功，$orderNo")
                            handler.postDelayed(Runnable {
                                dialog.setMessage(PopupMessage.create_order_success)
                                dialog.show()
                                dismiss(20 * 1000)
                            }, 0)
                        } else {
                            LogHelper.i(MyApp.NAYAX_TAG, "订单创建失败，请重试")
                            handler.postDelayed(Runnable {
                                dialog.setMessage(PopupMessage.create_order_fail)
                                dialog.show()
                                dismiss(10 * 1000)
                            }, 0)
                        }
                    } catch (e: IOException) {
                        LogHelper.i(MyApp.NAYAX_TAG, e.message!!)
                    }
                }
                //重新进入刷卡模式
                try {
                    val emvCoreStatus = getEmvCoreHandle().GetStatus()
                    LogHelper.i(MyApp.NAYAX_TAG, "重新进入刷卡模式, $emvCoreStatus")
                    if (emvCoreStatus == EmvCoreStatus.Ready) {
                        preAuth()
                    }
                } catch (e: EmvCoreException) {
                    LogHelper.e(MyApp.NAYAX_TAG, e.message!!)
                }
            }

            override fun CardTokenReceived(tokenDetails: TokenDetails?) {
                LogHelper.i(MyApp.NAYAX_TAG, "CardTokenReceived, $tokenDetails")
            }

            override fun ReaderMessage(readerMessage: ReaderMessage) {
                LogHelper.i(
                    MyApp.NAYAX_TAG,
                    readerMessage.Index.toString() + ": " + readerMessage.Line1 + "," + readerMessage.Line2
                )
            }
        })
    }

    /**
     * 检查屏幕亮度规则
     */
    fun modifyScreenBrightness() {
        try {
            val format = SimpleDateFormat("HH:mm", Locale.getDefault())
            val now = format.format(Date())  // 当前时间
//            LogHelper.i(MyApp.PLAYER_TAG,"检查屏幕亮度规则，当前时间：$now")

            // 示例：将 Date 转换为毫秒值进行比较
            val currentTime = format.parse(now)?.time ?: 0L

            var map = SharePreferencesUtil.getFromSharedPreferences(this, "brightness_config")
            if (map == null) {
                map = mutableMapOf("00:00-24:00" to 255.0) as Map<String, Any>?
            }

            val times = map.keys
            for (time in times) {
                val (startTimeStr, endTimeStr) = time.split("-")
                val brightnessValue = (map[time] as? Double)?.toInt() ?: 0

                val startTime = format.parse(startTimeStr)?.time ?: 0L
                val endTime = format.parse(endTimeStr)?.time ?: 0L

                // 判断当前时间是否在区间内，区间是闭区间左开右闭，即 [startTime, endTime)
                if (startTime > endTime) {
                    // 处理跨午夜的情况
                    if (currentTime >= startTime) {
                        setSystemBrightness(brightnessValue)
                        // LogHelper.i(MyApp.PLAYER_TAG, "当前时间 $now 在区间 $startTimeStr - $endTimeStr 内，亮度为：$brightnessValue")
                    }
                    if (currentTime < endTime) {
                        setSystemBrightness(brightnessValue)
                        // LogHelper.i(MyApp.PLAYER_TAG, "当前时间 $now 在区间 $startTimeStr - $endTimeStr 内，亮度为：$brightnessValue")
                    }
                } else {
                    // 非跨午夜的时间区间处理
                    if (currentTime >= startTime && currentTime < endTime) {
                        setSystemBrightness(brightnessValue)
                        // LogHelper.i(MyApp.PLAYER_TAG, "当前时间 $now 在区间 $startTimeStr - $endTimeStr 内，亮度为：$brightnessValue")
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 修改屏幕亮度
     */
    private fun setSystemBrightness(brightnessValue: Int) {
        if (Settings.System.canWrite(this)) {
            // 修改系统的屏幕亮度值
            Settings.System.putInt(
                this.contentResolver,
                Settings.System.SCREEN_BRIGHTNESS,
                brightnessValue
            )
        } else {
            LogHelper.e(MyApp.PLAYER_TAG, "没有WRITE_SETTINGS权限，无法修改亮度")
        }
    }
}