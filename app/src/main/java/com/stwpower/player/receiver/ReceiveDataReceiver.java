package com.stwpower.player.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.stwpower.player.base.MyApp;
import com.stwpower.player.utils.LogHelper;

public class ReceiveDataReceiver extends BroadcastReceiver {
    public static final String ACTION_SHOW_DIALOG = "com.stwpower.player.ACTION_SHOW_DIALOG";
    public static final String EXTRA_DIALOG_DATA = "EXTRA_DIALOG_DATA";
    public static final String ACTION_CHARGE_LINK_DATA = "com.stwpower.player.ACTION_CHARGE_LINK_DATA";
    public static final String CHARGE_LINK_DATA = "CHARGE_LINK_DATA";
    @Override
    public void onReceive(Context context, Intent intent) {
        if ("com.stwpower.player.ACTION_RECEIVE_DATA".equals(intent.getAction())) {
            CharSequence[] receivedArray = intent.getCharSequenceArrayExtra("listData");
            if (receivedArray != null) {
                LogHelper.INSTANCE.i(MyApp.NEW_BACK,"接收到归还充电宝数据");
                Intent dialogIntent = new Intent(ACTION_SHOW_DIALOG);
                for (CharSequence charSequence : receivedArray) {
                    LogHelper.INSTANCE.i(MyApp.NEW_BACK,charSequence.toString());
                    // 发送本地广播
                    dialogIntent.putExtra(EXTRA_DIALOG_DATA, charSequence.toString());
                    LocalBroadcastManager.getInstance(context).sendBroadcast(dialogIntent);
                }
            }

            String player = intent.getStringExtra("player");
            if (player != null) {
                LogHelper.INSTANCE.i(MyApp.PLAYER_TAG,"接收到业务指令：" + player);
                Intent playerIntent = new Intent(ACTION_CHARGE_LINK_DATA);
                // 发送本地广播
                playerIntent.putExtra(CHARGE_LINK_DATA, player);
                LocalBroadcastManager.getInstance(context).sendBroadcast(playerIntent);
            }
        }
    }
}
