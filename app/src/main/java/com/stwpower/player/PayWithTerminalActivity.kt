package com.stwpower.player

import android.content.pm.ActivityInfo
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import android.widget.ProgressBar
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.stwpower.player.base.MyApp
import com.stwpower.player.utils.LogHelper

class PayWithTerminalActivity : AppCompatActivity() {

    private lateinit var btnBack: Button
    private lateinit var tvPaymentInfo: TextView
    private lateinit var tvStatus: TextView
    private lateinit var tvAdditionalInfo: TextView
    private lateinit var tvInstruction: TextView
    private lateinit var progressBar: ProgressBar
    
    private var isPaymentCompleted = false
    private val handler = Handler(Looper.getMainLooper())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogHelper.i(MyApp.PLAYER_TAG, "PayWithTerminalActivity onCreate")
        
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        hideSystemUI()
        
        // Set landscape orientation
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        
        setContentView(R.layout.activity_pay_terminal)
        
        initViews()
        setupClickListeners()
        startPaymentProcess()
    }

    private fun hideSystemUI() {
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window.setStatusBarColor(Color.TRANSPARENT)
    }

    private fun initViews() {
        btnBack = findViewById(R.id.btn_back)
        tvPaymentInfo = findViewById(R.id.tv_payment_info)
        tvStatus = findViewById(R.id.tv_status)
        tvAdditionalInfo = findViewById(R.id.tv_additional_info)
        tvInstruction = findViewById(R.id.tv_instruction)
        progressBar = findViewById(R.id.progress_bar)
        
        // Initially disable back button
        btnBack.isEnabled = false
        btnBack.alpha = 0.5f
    }

    private fun setupClickListeners() {
        btnBack.setOnClickListener {
            if (isPaymentCompleted) {
                LogHelper.i(MyApp.PLAYER_TAG, "Back button clicked - returning to home")
                finish()
            }
        }
    }

    private fun startPaymentProcess() {
        LogHelper.i(MyApp.PLAYER_TAG, "Starting terminal payment process")
        
        // Simulate payment process
        updateStatus(getString(R.string.terminal_status_waiting))
        
        // After 3 seconds, show processing
        handler.postDelayed({
            updateStatus(getString(R.string.terminal_status_processing))
            tvInstruction.text = "Processing..."
        }, 3000)
        
        // After 8 seconds, show completed
        handler.postDelayed({
            paymentCompleted()
        }, 8000)
    }

    private fun updateStatus(status: String) {
        tvStatus.text = status
        LogHelper.i(MyApp.PLAYER_TAG, "Payment status updated: $status")
    }

    private fun paymentCompleted() {
        isPaymentCompleted = true
        updateStatus(getString(R.string.terminal_status_completed))
        tvInstruction.text = "Payment\nSuccessful!"
        tvAdditionalInfo.text = "Thank you for your payment. You can now go back to the home screen."
        
        // Hide progress bar
        progressBar.visibility = View.GONE
        
        // Enable back button
        btnBack.isEnabled = true
        btnBack.alpha = 1.0f
        
        LogHelper.i(MyApp.PLAYER_TAG, "Terminal payment completed successfully")
        
        // Auto return to home after 5 seconds
        handler.postDelayed({
            if (!isFinishing) {
                finish()
            }
        }, 5000)
    }

    override fun onResume() {
        super.onResume()
        hideSystemUI()
    }

    override fun onBackPressed() {
        if (isPaymentCompleted) {
            super.onBackPressed()
        } else {
            LogHelper.i(MyApp.PLAYER_TAG, "Back button disabled during payment process")
        }
    }

    override fun onDestroy() {
        handler.removeCallbacksAndMessages(null)
        super.onDestroy()
    }
}
