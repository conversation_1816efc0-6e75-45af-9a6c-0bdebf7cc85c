package com.stwpower.player

import android.content.pm.ActivityInfo
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import com.stwpower.player.base.MyApp
import com.stwpower.player.utils.LogHelper
import com.blankj.utilcode.util.SPUtils
import com.tamsiree.rxfeature.tool.RxQRCode

class PayWithAppActivity : AppCompatActivity() {

    private lateinit var btnBack: Button
    private lateinit var tvPaymentInfo: TextView
    private lateinit var tvInstructions: TextView
    private lateinit var tvStatus: TextView
    private lateinit var tvQrTitle: TextView
    private lateinit var tvQrInfo: TextView
    private lateinit var ivQrCode: AppCompatImageView
    
    private var isPaymentCompleted = false
    private val handler = Handler(Looper.getMainLooper())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogHelper.i(MyApp.PLAYER_TAG, "PayWithAppActivity onCreate")
        
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        hideSystemUI()
        
        // Set landscape orientation
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        
        setContentView(R.layout.activity_pay_app)
        
        initViews()
        setupClickListeners()
        generateQRCode()
        startPaymentProcess()
    }

    private fun hideSystemUI() {
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window.setStatusBarColor(Color.TRANSPARENT)
    }

    private fun initViews() {
        btnBack = findViewById(R.id.btn_back)
        tvPaymentInfo = findViewById(R.id.tv_payment_info)
        tvInstructions = findViewById(R.id.tv_instructions)
        tvStatus = findViewById(R.id.tv_status)
        tvQrTitle = findViewById(R.id.tv_qr_title)
        tvQrInfo = findViewById(R.id.tv_qr_info)
        ivQrCode = findViewById(R.id.iv_qr_code)
        
        // Initially disable back button
        btnBack.isEnabled = false
        btnBack.alpha = 0.5f
    }

    private fun setupClickListeners() {
        btnBack.setOnClickListener {
            if (isPaymentCompleted) {
                LogHelper.i(MyApp.PLAYER_TAG, "Back button clicked - returning to home")
                finish()
            }
        }
    }

    private fun generateQRCode() {
        // Use existing QR code from SPUtils or generate a payment QR code
        val qrCodeText = SPUtils.getInstance().getString("QR_CODE", "https://example.com/payment/12345")
        
        if (qrCodeText.isNotEmpty()) {
            try {
                RxQRCode.builder(qrCodeText)
                    .backColor(Color.WHITE)
                    .codeColor(Color.BLACK)
                    .codeSide(400)
                    .codeBorder(1)
                    .into(ivQrCode)
                LogHelper.i(MyApp.PLAYER_TAG, "QR Code generated successfully")
            } catch (e: Exception) {
                LogHelper.e(MyApp.PLAYER_TAG, "Failed to generate QR code: ${e.message}")
            }
        }
    }

    private fun startPaymentProcess() {
        LogHelper.i(MyApp.PLAYER_TAG, "Starting app payment process")
        
        // Simulate payment process
        updateStatus("Waiting for payment...")
        
        // After 5 seconds, show processing
        handler.postDelayed({
            updateStatus("Processing payment...")
            tvQrInfo.text = "Payment detected, processing..."
        }, 5000)
        
        // After 10 seconds, show completed
        handler.postDelayed({
            paymentCompleted()
        }, 10000)
    }

    private fun updateStatus(status: String) {
        tvStatus.text = status
        LogHelper.i(MyApp.PLAYER_TAG, "Payment status updated: $status")
    }

    private fun paymentCompleted() {
        isPaymentCompleted = true
        updateStatus("Payment completed successfully!")
        tvQrTitle.text = "Payment Successful!"
        tvQrInfo.text = "Thank you for your payment"
        
        // Enable back button
        btnBack.isEnabled = true
        btnBack.alpha = 1.0f
        
        LogHelper.i(MyApp.PLAYER_TAG, "App payment completed successfully")
        
        // Auto return to home after 5 seconds
        handler.postDelayed({
            if (!isFinishing) {
                finish()
            }
        }, 5000)
    }

    override fun onResume() {
        super.onResume()
        hideSystemUI()
    }

    override fun onBackPressed() {
        if (isPaymentCompleted) {
            super.onBackPressed()
        } else {
            LogHelper.i(MyApp.PLAYER_TAG, "Back button disabled during payment process")
        }
    }

    override fun onDestroy() {
        handler.removeCallbacksAndMessages(null)
        super.onDestroy()
    }
}
