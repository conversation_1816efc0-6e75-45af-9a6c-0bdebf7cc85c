package com.stwpower.player.utils

import org.apache.log4j.Level
import org.apache.log4j.Logger
import java.text.SimpleDateFormat
import java.util.*
import android.util.Log

object LogHelper {

    private val logger: Logger = Logger.getLogger(LogHelper::class.java)

    fun i(tag: String, msg: String) = log(Level.INFO, tag, msg)

    fun e(tag: String, msg: String) = log(Level.ERROR, tag, msg)

    fun d(tag: String, msg: String) = log(Level.DEBUG, tag, msg)

    fun w(tag: String, msg: String) = log(Level.WARN, tag, msg)

    fun v(tag: String, msg: String) = log(Level.TRACE, tag, msg)

    private fun log(level: Level, tag: String, msg: String) {
        val stackTrace = Thread.currentThread().stackTrace
        val caller = findCaller(stackTrace)
        caller?.let {
            val callerInfo = "${it.fileName}.${it.methodName}(Line:${it.lineNumber})"
            val logMsg = "$callerInfo - $msg"
            // 输出到Logcat
            Log.println(toAndroidLogLevel(level), tag, "${getCurrentTime()} - $logMsg")
            // 输出到文件，这里不再添加时间和调用者信息，因为Log4j会根据配置添加
            logger.log(level, "${String.format("%-8.8s", tag)} - $logMsg")
        }
    }

    private fun findCaller(stackTrace: Array<StackTraceElement>): StackTraceElement? {
        var hitLogHelper = false
        for (element in stackTrace) {
            if (element.className == LogHelper::class.java.name) {
                hitLogHelper = true
            } else if (hitLogHelper) {
                return element
            }
        }
        return null
    }

    private fun getCurrentTime(): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return sdf.format(Date())
    }

    private fun toAndroidLogLevel(level: Level): Int {
        return when {
            level.isGreaterOrEqual(Level.ERROR) -> Log.ERROR
            level.isGreaterOrEqual(Level.WARN) -> Log.WARN
            level.isGreaterOrEqual(Level.INFO) -> Log.INFO
            level.isGreaterOrEqual(Level.DEBUG) -> Log.DEBUG
            else -> Log.VERBOSE
        }
    }
}