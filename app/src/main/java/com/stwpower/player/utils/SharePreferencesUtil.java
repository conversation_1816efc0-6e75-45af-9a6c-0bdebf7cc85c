package com.stwpower.player.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.google.gson.Gson;

import java.util.Map;

public class SharePreferencesUtil {
    public static void saveToSharePreferences(Context context, String key, Map<String, Object> data){
        SharedPreferences config = context.getSharedPreferences("config", Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = config.edit();

        Gson gson = new Gson();
        String jsonData = gson.toJson(data);

        editor.putString(key,jsonData);
        editor.apply();
    }

    public static Map<String, Object> getFromSharedPreferences(Context context, String key){
        SharedPreferences config = context.getSharedPreferences("config", Context.MODE_PRIVATE);
        String jsonData = config.getString(key, "");
        if(!jsonData.isEmpty()){
            Gson gson = new Gson();
            return gson.fromJson(jsonData, Map.class);
        }
        return null;
    }
}
