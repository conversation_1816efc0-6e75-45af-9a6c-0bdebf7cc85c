package com.stwpower.player.utils;

import android.content.Context;
import android.os.Environment;

import com.stwpower.player.base.MyApp;


import java.io.File;


public class MyUtil {

    public static String getFNo(Context context) {
        if (MyApp.fno_type.equals("c")) {
            return FileUtils.ReadTxtFile(getDevicePath());
        } else if(MyApp.fno_type.equals("b")){
            return FileUtils.ReadTxtFile(getDevicePath_WS());
        }else {
            return null;
        }
    }

    private static String getDevicePath() {
        return Environment.getExternalStorageDirectory()
                + File.separator + "data" + File.separator + "DEVICES.txt";  //  /sdcard/data/DEVICES.txt
    }
    private static String getDevicePath_WS() {
        return Environment.getExternalStorageDirectory()
                + File.separator + "devinfo.txt";  //  /sdcard/devinfo.txt
    }
}
