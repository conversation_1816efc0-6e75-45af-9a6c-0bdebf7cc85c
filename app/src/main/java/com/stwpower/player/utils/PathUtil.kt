package com.stwpower.player.utils

import com.blankj.utilcode.util.SPUtils
import com.stwpower.player.base.MyApp
import com.stwpower.player.bean.VideoAdvertisingInfo
import com.google.gson.reflect.TypeToken
import com.google.gson.Gson
import com.stwpower.player.constant.Constants
import java.io.File

object PathUtil {

    const val VIDEO_PATHS = "videoPath"
    val type = object : TypeToken<MutableMap<String, String>>(){}.type

    @Synchronized fun savePath(url: String, path: String) {
        // SP存储
        val map = getPathMap()
        map[url] = path
        SPUtils.getInstance().put(VIDEO_PATHS, map)
        
        // 文件系统备份
        savePathToFile(map)
    }
    
    private fun savePathToFile(map: Map<String, String>) {
        try {
            val file = File(Constants.getFileDirectory(), "path_map.json")
            file.writeText(Gson().toJson(map))
        } catch (e: Exception) {
            LogHelper.e(MyApp.PLAYER_TAG, "保存路径映射文件失败: ${e.message}")
        }
    }
    
    private fun loadPathFromFile(): Map<String, String>? {
        try {
            val file = File(Constants.getFileDirectory(), "path_map.json")
            if (file.exists()) {
                return Gson().fromJson(file.readText(), type)
            }
        } catch (e: Exception) {
            LogHelper.e(MyApp.PLAYER_TAG, "读取路径映射文件失败: ${e.message}")
        }
        return null
    }
    
    @Synchronized fun getPathMap(): MutableMap<String, String> {
        // 先从SP获取
        var map = SPUtils.getInstance().getObject<MutableMap<String, String>>(VIDEO_PATHS, type)
        if (map == null) {
            // 尝试从文件恢复
            map = loadPathFromFile()?.toMutableMap()
            if (map != null) {
                // 恢复SP存储
                SPUtils.getInstance().put(VIDEO_PATHS, map)
            }
        }
        return map ?: HashMap()
    }

    fun loadPathFromFile(originalContent: String): String? {
        try {
            val file = File(Constants.getFileDirectory(), "path_map.json")
            if (file.exists()) {
                val map: Map<String, String> = Gson().fromJson(file.readText(), type)
                return map[originalContent] // 根据原始 content 获取对应的本地路径
            }
        } catch (e: Exception) {
            LogHelper.e(MyApp.PLAYER_TAG, "读取路径映射文件失败: ${e.message}")
        }
        return null
    }

    @Synchronized fun deleteVideoPath(url: String){
        val map = getPathMap()
        if (map.containsKey(url)){
            FileUtils.deleteVideo(map[url])
            map.remove(url)
            SPUtils.getInstance().put(VIDEO_PATHS,map)
        }
    }

    @Synchronized fun deleteAllVideo(){
        FileUtils.deleteVideo()
        val map = getPathMap()
        map.clear()
        SPUtils.getInstance().put(VIDEO_PATHS,map)
    }

    fun checkChange(newAd: VideoAdvertisingInfo?, oldAd:VideoAdvertisingInfo?){
        LogHelper.i(MyApp.PLAYER_TAG,"是否相同:${newAd==oldAd}---newAd:$newAd---oldAd:$oldAd")
        //这里只需要删除无用的广告列表
        if (newAd == oldAd){
            return
        }
        if (oldAd == null){
            return
        }
        if (newAd == null ||newAd.data.isEmpty()){
            //新的广告列表为空,把旧的全部删除
            deleteAllVideo()
//            LogHelper.i(MyApp.PLAYER_TAG,"新的广告列表为空,把旧的全部删除");
        }else{
            val newUrls:MutableList<String> = ArrayList()
            newAd.data.forEach {
                if (it.cover_type == 1 || it.cover_type == 2){
                    newUrls.add(it.content)
                }
            }
            oldAd.data.forEach {
                if ((it.cover_type ==1||it.cover_type ==2)  && !newUrls.contains(it.content)){
                    //删除这个视频
                    deleteVideoPath(it.content)
//                    LogHelper.i(MyApp.PLAYER_TAG,"删除这个视频"+it.content);
                }
            }
        }
    }
}