package com.stwpower.player.utils

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.Window
import android.widget.TextView
import com.stwpower.player.R
import com.stwpower.player.base.MyApp

class CustomDialog(context: Context, private val activity: Activity, private var message: String) : Dialog(context) {
    private lateinit var messageTextView: TextView
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)

        if(MyApp.protrait){
            setContentView(R.layout.custom_dialog_layout_24)
        }else{
            setContentView(R.layout.custom_dialog_layout)
        }

        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT)) // 设置背景为透明

        // 设置提示信息
        messageTextView = findViewById(R.id.dialog_message)
        messageTextView.text = message

        // 让对话框在屏幕中居中
        val windowParams = window?.attributes
        windowParams?.gravity = Gravity.CENTER
        window?.attributes = windowParams

        // 隐藏导航栏
        window?.decorView?.systemUiVisibility = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
    }

    override fun dismiss() {
        super.dismiss()
        // 重新设置标志来隐藏导航栏
        activity.window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
    }

    fun setMessage(message: String) {
        this.message = message
        if(::messageTextView.isInitialized) {
            messageTextView.text = message
        }
    }
}