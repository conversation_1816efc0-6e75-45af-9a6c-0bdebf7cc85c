package com.stwpower.player.utils

import com.blankj.utilcode.util.BusUtils
import com.stwpower.player.base.MyApp
import com.stwpower.player.bean.RuleInfo
import com.stwpower.player.constant.Constants
import com.stwpower.player.even.DownloadFailedEvent
import com.stwpower.player.even.DownloadFinishEvent
import com.stwpower.player.room.RuleDataBase
import java.io.File
import java.util.UUID
import java.util.concurrent.Executors
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.schedulers.Schedulers
import org.greenrobot.eventbus.EventBus
import java.io.IOException
import java.io.RandomAccessFile
import java.net.HttpURLConnection
import java.net.URL

object DownLoadUtil {

    private val cachedThreadPool = Executors.newCachedThreadPool()

    //正在下载中的任务
    val downloadTask: MutableList<String> = ArrayList()
    private val ruleDao = RuleDataBase.getInstance(MyApp.instance.applicationContext).ruleDao()

    fun downLoadUrl(ruleInfo: RuleInfo) {
        if (ruleInfo.content.startsWith("http://") || ruleInfo.content.startsWith("https://")) {
            var pathMap = PathUtil.getPathMap()
            if (PathUtil.getPathMap().containsKey(ruleInfo.content)) {
                LogHelper.i(MyApp.PLAYER_TAG,"此路径的视频已经下载过了，直接播放")
                cachedThreadPool.execute {
                    //为首SC20 wifi板子这里重新赋值后，content会为空
                    val originalPath = ruleInfo.content

                    // 判断原始路径是否为本地路径
                    if (isLocalPath(originalPath)) {
                        // 如果是本地路径，直接使用
                        ruleInfo.content = originalPath
                        LogHelper.i(MyApp.PLAYER_TAG, "原始路径为本地路径，直接使用：$originalPath")
                    } else {
                        // 尝试从数据库获取路径
                        val localPath = PathUtil.getPathMap()[originalPath]
                        if (localPath.isNullOrEmpty()) {
                            // 尝试从 JSON 文件中获取本地路径
                            val jsonPath = PathUtil.loadPathFromFile(originalPath)
                            LogHelper.i(MyApp.PLAYER_TAG,
                                "原始路径为：$originalPath，数据库获取到的路径为空，尝试从json文件中获取，json文件获取到的路径为：$jsonPath"
                            )
                            ruleInfo.content = jsonPath ?: ""
                        } else {
                            ruleInfo.content = localPath
                        }
                    }
                    ruleDao.updateRule(ruleInfo.content, ruleInfo.dataId)
                    EventBus.getDefault().post(DownloadFinishEvent(ruleInfo))
                }
            } else {
                realDownload(ruleInfo.content, ruleInfo)
            }
        } else {
            LogHelper.e(MyApp.PLAYER_TAG,"下载地址不对，下载失败")
            EventBus.getDefault().post(DownloadFailedEvent())
        }
    }

    private fun realDownload(url: String, ruleInfo: RuleInfo) {
        val directory = File(Constants.getFileDirectory())
        if (!directory.exists()) {
            directory.mkdirs() // 创建目录，包括所有必需但不存在的父目录
        }
        val point = url.lastIndexOf(".")
        val suffix = url.substring(point)
        val fileName = UUID.randomUUID().toString() + suffix
        val destPath = Constants.getFileDirectory() + File.separator + fileName
        // 使用RxJava进行文件下载
        Observable.fromCallable {
            val connection = URL(url).openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.connect()

            if (connection.responseCode != HttpURLConnection.HTTP_OK) {
                throw IOException("服务器返回了错误的响应代码: ${connection.responseCode}")
            }

            val fileSize = connection.contentLength
            val inputStream = connection.inputStream
            val file = File(destPath)
            val raf = RandomAccessFile(file, "rw")
            try {
                val buffer = ByteArray(8192)
                var bytesRead: Int
                var totalBytesRead = 0L

                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                    raf.write(buffer, 0, bytesRead)
                    totalBytesRead += bytesRead

                    // 仅在进度变化较大时更新UI，减少UI刷新次数
                    val progress = (totalBytesRead * 100 / fileSize).toInt()
                    if (progress % 10 == 0) {
                        LogHelper.i(MyApp.PLAYER_TAG,"下载进度：$progress% 已下载：$totalBytesRead bytes / $fileSize bytes")
                    }
                }
            } finally {
                raf.close()
                inputStream.close()
                connection.disconnect()
            }

            file
        }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .subscribe({
                val path = "file://${it.absolutePath}"
                LogHelper.i(MyApp.PLAYER_TAG,"视频下载成功: $path")

                // 将文件路径和请求地址保存到数据库
                cachedThreadPool.execute {
                    PathUtil.savePath(url, it.absolutePath)
                    ruleInfo.content = path
                    ruleDao.updateRule(ruleInfo.content, ruleInfo.dataId)
                    downloadTask.remove(url)

                    // 通知界面更新
                    EventBus.getDefault().post(DownloadFinishEvent(ruleInfo))
                }
            }, {
                //TODO 到这里的时候，视频会卡住，不会继续播放本地视频
                LogHelper.i(MyApp.PLAYER_TAG,"下载失败：${it.message}")
                val file = File(destPath)
                file.delete()
                downloadTask.remove(ruleInfo.content)
                EventBus.getDefault().post(DownloadFailedEvent())
            })
    }

    // 判断路径是否为本地路径的辅助方法
    private fun isLocalPath(path: String): Boolean {
        return path.startsWith("/") || path.startsWith("file://") || path.startsWith("content://")
    }
}