package com.stwpower.player.utils;

import com.blankj.utilcode.util.ToastUtils;
import com.stwpower.player.base.MyApp;
import com.stwpower.player.constant.Constants;

import org.apache.log4j.Logger;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

/**
 * 作者：ly-xuxiaolong
 * 版本：1.0
 * 创建日期：2020/3/9
 * 描述：
 * 修订历史：
 */
public class FileUtils{

    private static Logger logger = Logger.getLogger(FileUtils.class);

    public static String ReadTxtFile(String strFilePath){
        String path = strFilePath;
        String content = ""; //文件内容字符串
        //打开文件
        File file = new File(path);
        //如果path是传递过来的参数，可以做一个非目录的判断
        if (file.isDirectory()){
            LogHelper.INSTANCE.e(MyApp.PLAYER_TAG,"The File doesn't exist.");
            ToastUtils.showShort("The File doesn't exist.");
        }else{
            try {
                InputStream instream = new FileInputStream(file);
                if (instream != null)
                {
                    InputStreamReader inputreader = new InputStreamReader(instream);
                    BufferedReader buffreader = new BufferedReader(inputreader);
                    String line;
                    //分行读取
                    while (( line = buffreader.readLine()) != null) {
                        content += line;
                    }
                    instream.close();
                }
            }
            catch (java.io.FileNotFoundException e)
            {
                LogHelper.INSTANCE.e(MyApp.PLAYER_TAG,"The File doesn't not exist.");
//                Log.d("TestFile", );
            }
            catch (IOException e)
            {
                LogHelper.INSTANCE.e(MyApp.PLAYER_TAG,e.getMessage());
            }
        }
        return content;
    }


    //删除下载的所有视频
    public static void deleteVideo(){
        File dir = new File(Constants.getFileDirectory());
        File[] files = dir.listFiles();
        if(files != null){
            for (File file : files) {
                if (file.isFile()){
                    file.delete();
                }
            }
        }
    }

    public static void deleteVideo(String path){
        File file = new File(path);
        if (file.exists() && file.isFile()){
            boolean delete = file.delete();
            LogHelper.INSTANCE.i(MyApp.PLAYER_TAG,"删除---"+path+"----是否成功："+delete);
        }
    }
}
