package com.stwpower.player.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import androidx.fragment.app.Fragment
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.SPUtils
import kotlin.reflect.KClass
import java.lang.reflect.Type

fun SPUtils.put(key:String,any: Any){
    val toJson = GsonUtils.toJson(any)
    SPUtils.getInstance().put(key,toJson)
}

fun <T> SPUtils.getObject(key:String,type:Class<T>):T?{
    val json = SPUtils.getInstance().getString(key)
    return if (TextUtils.isEmpty(json)){
        null
    }else{
        GsonUtils.fromJson(json, type)
    }
}

fun <T> SPUtils.getObject(key:String,type:Type):T?{
    val json = SPUtils.getInstance().getString(key)
    return if (TextUtils.isEmpty(json)){
        null
    }else{
        GsonUtils.fromJson(json, type)
    }
}

inline fun <reified T : Activity> Activity.start() {
    startActivity(Intent(this, T::class.java))
}

inline fun <reified T : Activity> Activity.start(mKey: String, mBundle: Bundle?) {
    val mIntent = Intent(this, T::class.java)
    mIntent.putExtra(mKey, mBundle)
    startActivity(mIntent)
}

/**启动Activity*/
inline fun <reified T : Activity> Activity.start(vararg pair: Pair<String, String>?) {
    val mIntent = Intent(this, T::class.java)
    pair?.let {
        pair.forEach {
            mIntent.putExtra(it!!.first, it!!.second)
        }
    }
    startActivity(mIntent)
}

/**启动Activity*/
inline fun <reified T : Activity> Context.start() {
    startActivity(Intent(this, T::class.java))
}

/**启动Activity*/
inline fun <reified T : Activity> Context.start(mKey: String, mBundle: Bundle?) {
    val mIntent = Intent(this, T::class.java)
    mIntent.putExtra(mKey, mBundle)
    startActivity(mIntent)
}

/**启动Activity*/
inline fun <reified T : Activity> Context.start(vararg pair: Pair<String, String>?) {
    val mIntent = Intent(this, T::class.java)
    pair?.let {
        pair.forEach {
            mIntent.putExtra(it!!.first, it!!.second)
        }
    }
    startActivity(mIntent)
}

fun <T : Activity> Context.startActivity(clazz: KClass<T>, block: (Intent.() -> Unit)? = null) {
    val intent = Intent(this, clazz.java).apply {
        block?.invoke(this)
    }
    startActivity(intent)
}

fun <T : Activity> Activity.startActivity(clazz: KClass<T>, block: (Intent.() -> Unit)? = null) {
    val intent = Intent(this, clazz.java).apply {
        block?.invoke(this)
    }
    startActivity(intent)
}

fun <T : Activity> Fragment.startActivity(clazz: KClass<T>, block: (Intent.() -> Unit)? = null) {
    val intent = Intent(activity, clazz.java).apply {
        block?.invoke(this)
    }
    startActivity(intent)
}
