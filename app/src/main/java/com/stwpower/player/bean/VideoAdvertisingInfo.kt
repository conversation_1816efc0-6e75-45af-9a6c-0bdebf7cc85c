package com.stwpower.player.bean

data class VideoAdvertisingInfo(
    val code: Int,
    val data: List<RuleInfo>,
    val message: String
){
    override fun equals(other: Any?): <PERSON>olean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as VideoAdvertisingInfo

        if (code != other.code) return false
        if (data != other.data) return false
        if (message != other.message) return false

        return true
    }

    override fun hashCode(): Int {
        var result = code
        result = 31 * result + data.hashCode()
        result = 31 * result + message.hashCode()
        return result
    }
}

