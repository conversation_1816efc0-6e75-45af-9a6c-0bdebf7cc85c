package com.stwpower.player.bean;

import android.content.Context;

import com.stwpower.player.constant.Constants;

import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;

public class PopupMessage {
    public static String upgrading_start = "Upgrading, please do not turn off the power, progress 0%.";
    public static String upgrading = "Upgrading, please do not turn off the power, progress";
    public static String connect_success = "Connect USB Reader Success";
    public static String collection_success = "Collection of payment methods successful, please wait.";
    public static String collection_fail = "Collection of payment methods failed, please try again.";
    public static String create_order_success = "Order created successfully, please check later if the power bank has popped out. If it hasn't, please try again.";
    public static String create_order_fail = "Failed to create order, please try again.";
    public static String return_message = "You rented for TIME you have been charged MONEY";

    public static void initialize() {
        String filePath = Constants.getConfigDirectory() + "popup_messages.json";

        try {
            // 创建File对象
            File file = new File(filePath);
            if (!file.exists()) {
                return; // 文件不存在，使用默认值
            }
            // 创建FileInputStream对象
            FileInputStream fileInputStream = new FileInputStream(file);
            // 创建BufferedReader对象
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(fileInputStream));
            StringBuilder jsonString = new StringBuilder();
            String line;
            // 读取文件内容
            while ((line = bufferedReader.readLine()) != null) {
                jsonString.append(line);
            }
            // 关闭流
            bufferedReader.close();
            fileInputStream.close();

            // 解析JSON字符串
            JSONObject jsonObject = new JSONObject(jsonString.toString());

            // 初始化静态变量
            upgrading_start = jsonObject.optString("upgrading_start", upgrading_start);
            upgrading = jsonObject.optString("upgrading", upgrading);
            connect_success = jsonObject.optString("connect_success", connect_success);
            collection_success = jsonObject.optString("collection_success", collection_success);
            collection_fail = jsonObject.optString("collection_fail", collection_fail);
            create_order_success = jsonObject.optString("create_order_success", create_order_success);
            create_order_fail = jsonObject.optString("create_order_fail", create_order_fail);
            return_message = jsonObject.optString("return_message", return_message);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
