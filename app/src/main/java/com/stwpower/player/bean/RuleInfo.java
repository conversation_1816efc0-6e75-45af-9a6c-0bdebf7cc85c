package com.stwpower.player.bean;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.stwpower.player.room.UrlConverters;
import com.google.common.base.Objects;

@Entity(tableName = "ruleInfo")
@TypeConverters(UrlConverters.class)
public class RuleInfo implements Cloneable{

    @PrimaryKey(autoGenerate = true)
    private Long dataId;
    private int id;
    @ColumnInfo(name = "cover_type")
    private int cover_type; //类型(0-图片, 1-视频, 2-URL)
    private String description;//内容
    @ColumnInfo(name = "start_time")
    private String  start_time;//广告开始时间 (08:00)
    @ColumnInfo(name = "end_time")
    private String end_time;//广告结束时间 (12:00)
    @ColumnInfo(name = "content")
    private String content;//图片、视频 链接
    private String title;

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getCover_type() {
        return cover_type;
    }

    public void setCover_type(int cover_type) {
        this.cover_type = cover_type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStart_time() {
        return start_time;
    }

    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof RuleInfo)) return false;
        RuleInfo ruleInfo = (RuleInfo) o;
        return id == ruleInfo.id &&
                cover_type == ruleInfo.cover_type &&
                Objects.equal(dataId, ruleInfo.dataId) &&
                Objects.equal(description, ruleInfo.description) &&
                Objects.equal(start_time, ruleInfo.start_time) &&
                Objects.equal(end_time, ruleInfo.end_time) &&
                Objects.equal(title, ruleInfo.title);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(dataId, id, cover_type, description, start_time, end_time, title);
    }

    @Override
    public String toString() {
        return "RuleInfo{" +
                "dataId=" + dataId +
                ", id=" + id +
                ", cover_type=" + cover_type +
                ", description='" + description + '\'' +
                ", start_time='" + start_time + '\'' +
                ", end_time='" + end_time + '\'' +
                ", content='" + content + '\'' +
                ", title='" + title + '\'' +
                '}';
    }
}
