package com.stwpower.player;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

/**
 * 开机的广播接收
 */
public class BootUpReceiver extends BroadcastReceiver {
	private static final String ACTION_BOOT_COMPLETED = "android.intent.action.BOOT_COMPLETED";
	@Override
	public void onReceive(Context context, Intent intent) {
		String intentAction = intent.getAction();
		if (ACTION_BOOT_COMPLETED.equals(intentAction)) {
			//开机自启
//			LogUtils.d("BootUpReceiver = " + intentAction);
			Intent jumpIntent = new Intent(context, MainActivity.class);
			jumpIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
			context.startActivity(jumpIntent);
		}
	}

}
