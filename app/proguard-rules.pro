# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
-dontwarn com.tamsiree.rxtool.RxPhotoTool
-dontwarn com.tamsiree.rxui.activity.ActivityBase
-dontwarn com.tamsiree.rxui.view.ticker.RxTickerUtils
-dontwarn java.beans.BeanInfo
-dontwarn java.beans.IntrospectionException
-dontwarn java.beans.Introspector
-dontwarn java.beans.PropertyDescriptor

# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**

# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# OkHttp platform used only on JVM and when Conscrypt and other security providers are available.
-dontwarn okhttp3.internal.platform.**
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**

-dontwarn java.awt.Color
-dontwarn java.lang.management.ManagementFactory
-dontwarn java.lang.management.RuntimeMXBean
-dontwarn android.os.IRemoteCallback
-dontwarn android.stripe.os.BbposMap$1
-dontwarn android.stripe.os.BbposRemoteCallback$3
-dontwarn android.stripe.os.BbposResult$1
-dontwarn android.stripe.os.LastPinInfo$1
-dontwarn com.lzy.okgo.callback.StringCallback
-dontwarn com.tamsiree.rxtool.RxActivityTool
-dontwarn com.tamsiree.rxtool.RxBeepTool
-dontwarn com.tamsiree.rxtool.RxDataTool
-dontwarn com.tamsiree.rxtool.RxImageTool
-dontwarn com.tamsiree.rxtool.RxSPTool
-dontwarn com.tamsiree.rxtool.interfaces.OnSuccessAndErrorListener
-dontwarn com.tamsiree.rxtool.view.RxToast
-dontwarn com.tamsiree.rxui.view.dialog.RxDialogSure
-dontwarn com.tamsiree.rxui.view.ticker.RxTickerView
-dontwarn java.awt.Component
-dontwarn java.awt.Container
-dontwarn java.awt.Dimension
-dontwarn java.awt.FlowLayout
-dontwarn java.awt.Font
-dontwarn java.awt.Frame
-dontwarn java.awt.GridBagConstraints
-dontwarn java.awt.GridBagLayout
-dontwarn java.awt.Insets
-dontwarn java.awt.Label
-dontwarn java.awt.LayoutManager
-dontwarn java.awt.Toolkit
-dontwarn java.awt.Window
-dontwarn java.awt.event.ActionListener
-dontwarn java.awt.event.AdjustmentListener
-dontwarn java.awt.event.KeyAdapter
-dontwarn java.awt.event.MouseAdapter
-dontwarn java.awt.event.WindowAdapter
-dontwarn javax.jms.Destination
-dontwarn javax.jms.JMSException
-dontwarn javax.jms.Message
-dontwarn javax.jms.MessageListener
-dontwarn javax.jms.ObjectMessage
-dontwarn javax.jms.Topic
-dontwarn javax.jms.TopicConnection
-dontwarn javax.jms.TopicConnectionFactory
-dontwarn javax.jms.TopicPublisher
-dontwarn javax.jms.TopicSession
-dontwarn javax.mail.Address
-dontwarn javax.mail.Authenticator
-dontwarn javax.mail.BodyPart
-dontwarn javax.mail.Message$RecipientType
-dontwarn javax.mail.Message
-dontwarn javax.mail.MessagingException
-dontwarn javax.mail.Multipart
-dontwarn javax.mail.Session
-dontwarn javax.mail.Transport
-dontwarn javax.mail.internet.AddressException
-dontwarn javax.mail.internet.InternetAddress
-dontwarn javax.mail.internet.InternetHeaders
-dontwarn javax.mail.internet.MimeBodyPart
-dontwarn javax.mail.internet.MimeMessage
-dontwarn javax.mail.internet.MimeMultipart
-dontwarn javax.mail.internet.MimeUtility
-dontwarn javax.management.DynamicMBean
-dontwarn javax.management.MBeanRegistration
-dontwarn javax.management.Notification
-dontwarn javax.management.NotificationBroadcaster
-dontwarn javax.management.NotificationBroadcasterSupport
-dontwarn javax.management.NotificationListener
-dontwarn javax.naming.Context
-dontwarn javax.naming.InitialContext
-dontwarn javax.naming.NameNotFoundException
-dontwarn javax.naming.NamingException
-dontwarn javax.swing.AbstractAction
-dontwarn javax.swing.ImageIcon
-dontwarn javax.swing.JButton
-dontwarn javax.swing.JCheckBoxMenuItem
-dontwarn javax.swing.JComponent
-dontwarn javax.swing.JDialog
-dontwarn javax.swing.JFrame
-dontwarn javax.swing.JLabel
-dontwarn javax.swing.JPanel
-dontwarn javax.swing.JTable
-dontwarn javax.swing.JTree
-dontwarn javax.swing.SwingUtilities
-dontwarn javax.swing.event.DocumentListener
-dontwarn javax.swing.event.ListSelectionListener
-dontwarn javax.swing.event.TreeModelListener
-dontwarn javax.swing.table.AbstractTableModel
-dontwarn javax.swing.table.DefaultTableCellRenderer
-dontwarn javax.swing.table.DefaultTableModel
-dontwarn javax.swing.table.TableCellEditor
-dontwarn javax.swing.table.TableModel
-dontwarn javax.swing.tree.DefaultMutableTreeNode
-dontwarn javax.swing.tree.DefaultTreeCellEditor
-dontwarn javax.swing.tree.DefaultTreeCellRenderer
-dontwarn javax.swing.tree.DefaultTreeModel
-dontwarn javax.swing.tree.MutableTreeNode
-dontwarn javax.swing.tree.TreeCellEditor
-dontwarn javax.swing.tree.TreeNode

# 保留主入口点类和方法
-keep class com.stwpower.player.MainActivity { *; }
-keep class com.stwpower.player.bean.* { *; }

# 保留类和方法的注解
-keepattributes *Annotation*

# 保留被序列化的类
-keep class * implements java.io.Serializable { *; }

# 保留自定义视图类
-keep class * extends android.view.View { *; }

# 禁用对调试日志的混淆（推荐删除调试日志）
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    public static *** i(...);
    public static *** w(...);
    public static *** e(...);
}

# 保留 RxHttp 自动生成的类
-keep class rxhttp.wrapper.** { *; }

# 保留生成的 RxHttp 请求类
-keep class rxhttp.** { *; }

# 保留 RxHttp 使用的注解
-keepattributes *Annotation*

# 保留 RxJava 的类
-dontwarn io.reactivex.**
-keep class io.reactivex.** { *; }
-keep interface io.reactivex.** { *; }

# 保留 OkHttp 的类
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**

# 保留 Gson 的序列化字段（如果使用了 Gson 转换器）
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}
-keepattributes Signature
-keepattributes EnclosingMethod
-keepattributes InnerClasses
-dontwarn com.google.gson.**

# 保留 RxHttp 编译器生成的文件
-keepnames class *$$Lambda$* { *; }
-keepnames class rxhttp.** { *; }

# 保留注解
-keepattributes RuntimeVisibleAnnotations
-keepattributes AnnotationDefault

# 保留所有带 @SerializedName 注解的字段
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}


# Retrofit
-keep class retrofit2.Retrofit { *; }
-keep class retrofit2.** { *; }
-keep interface retrofit2.** { *; }
-keepclasseswithmembers class * {
    @retrofit2.http.* <methods>;
}
-keepattributes RuntimeVisibleAnnotations

# Gson
-keep class * {
    @com.google.gson.annotations.SerializedName <fields>;
}
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn com.google.gson.**

# OkHttp
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**

# 保留编译时注解
-keepattributes RuntimeVisibleAnnotations

# GSYVideoPlayer
-keep class com.shuyu.gsyvideoplayer.video.** { *; }
-dontwarn com.shuyu.gsyvideoplayer.video.**
-keep class com.shuyu.gsyvideoplayer.video.base.** { *; }
-dontwarn com.shuyu.gsyvideoplayer.video.base.**
-keep class com.shuyu.gsyvideoplayer.utils.** { *; }
-dontwarn com.shuyu.gsyvideoplayer.utils.**
-keep class com.shuyu.gsyvideoplayer.player.** {*;}
-dontwarn com.shuyu.gsyvideoplayer.player.**
-keep class tv.danmaku.ijk.** { *; }
-dontwarn tv.danmaku.ijk.**
-keep class androidx.media3.** {*;}
-keep interface androidx.media3.**

-keep class com.shuyu.alipay.** {*;}
-keep interface com.shuyu.alipay.**

-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, java.lang.Boolean);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# 保留 RxHttp 相关类
-keep class com.ljx.rxhttp.** { *; }

