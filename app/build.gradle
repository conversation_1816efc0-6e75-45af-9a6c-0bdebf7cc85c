plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'com.google.devtools.ksp'
}

android {
    compileSdkVersion 34
    defaultConfig {
        applicationId "com.stwpower.player"
        minSdkVersion 24
        targetSdkVersion 34
        versionCode 35
        versionName "V4.0.0"
        flavorDimensions "default"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            ndkVersion "22.1.7171670"
//            abiFilters "armeabi", "armeabi-v7a"
            // 模拟器调试
            abiFilters "armeabi", "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
        }
    }

    signingConfigs {
        releaseRK3288 {
            keyAlias 'platform'
            keyPassword 'stw2024'
            storeFile file('RK3288.keystore')
            storePassword 'stw2024'
        }
        releaseSC20 {
            keyAlias 'platform'
            keyPassword 'stw2024'
            storeFile file('SC20.keystore')
            storePassword 'stw2024'
        }
        releaseRK3288NEW {
            keyAlias 'platform'
            keyPassword 'stw2024'
            storeFile file('RK3288-NEW.keystore')
            storePassword 'stw2024'
        }
        releaseSC20NEW {
            keyAlias 'platform'
            keyPassword 'stw2024'
            storeFile file('SC20-NEW.keystore')
            storePassword 'stw2024'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        applicationVariants.all {
            variant ->
                variant.outputs.all {
                    outputFileName = "advertising_${variant.name}_${variant.versionName}.apk"
                }
        }
        debug {
            signingConfig signingConfigs.releaseSC20NEW
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        targetCompatibility JavaVersion.VERSION_17
        sourceCompatibility JavaVersion.VERSION_17
    }

    android.applicationVariants.all { variant ->
        variant.outputs.all {
            def flavor = variant.productFlavors[0].name
            outputFileName = "Player-V4${defaultConfig.versionCode}-${flavor}.apk"
        }
    }

    productFlavors {
        RK3288 {
            versionName "${defaultConfig.versionName}"
            signingConfig signingConfigs.releaseRK3288
        }
        SC20 {
            versionName "${defaultConfig.versionName}"
            signingConfig signingConfigs.releaseSC20
        }
        RK3288NEW {
            versionName "${defaultConfig.versionName}"
            signingConfig signingConfigs.releaseRK3288NEW
        }
        SC20NEW {
            versionName "${defaultConfig.versionName}"
            signingConfig signingConfigs.releaseSC20NEW
        }
    }
    namespace 'com.stwpower.player'
}

ext {
    kotlin_coroutines_version = '1.7.1'
    okhttp_version = '4.11.0'
    retrofit_version = '2.9.0'
    stripeTerminalVersion = "2.23.2"
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation project(':EmvCoreApi')
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.1'
    implementation 'com.blankj:utilcode:1.30.7'

    //消息总线
    implementation 'org.greenrobot:eventbus:3.3.1'

    //必须3个为必须
    implementation 'com.squareup.okhttp3:okhttp:4.9.0'
    //lifecycleScope
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.6.2"
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.2'
    implementation 'io.reactivex.rxjava3:rxjava:3.1.5'

    //功能库（Zxing扫描与生成二维码条形码 支付宝 微信）
    implementation 'com.github.tamsiree.RxTool:RxFeature:v2.4.3'

    implementation 'com.permissionx.guolindev:permissionx:1.3.0'
    implementation 'com.uber.autodispose:autodispose-android-archcomponents:0.8.0'

    //日志
    implementation 'org.slf4j:slf4j-log4j12:1.7.24'
    implementation 'de.mindpipe.android:android-logging-log4j:1.0.3'

    //视频播放器
    implementation 'com.github.CarGuo.GSYVideoPlayer:gsyvideoplayer-java:v10.0.0'
    //是否需要ExoPlayer模式
    implementation 'com.github.CarGuo.GSYVideoPlayer:gsyvideoplayer-exo2:v10.0.0'

    implementation 'com.gyf.immersionbar:immersionbar:3.0.0'

    implementation 'com.github.bumptech.glide:glide:4.11.0'
    kapt 'com.github.bumptech.glide:compiler:4.11.0'

    // room
    implementation 'androidx.room:room-runtime:2.5.0'
    kapt 'androidx.room:room-compiler:2.5.0'

    //bugly
    implementation 'com.tencent.bugly:crashreport:2.1.9'
    implementation 'com.tencent.bugly:nativecrashreport:3.0'

    implementation "androidx.core:core-ktx:1.10.1"
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

    // Stripe Terminal library
    implementation "com.stripe:stripeterminal:$stripeTerminalVersion"
    // Retrofit
    implementation "com.squareup.retrofit2:retrofit:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-gson:$retrofit_version"
    implementation "com.google.android.material:material:1.9.0"
    //WorkManager
    implementation 'androidx.work:work-runtime:2.7.1'
}

repositories {
    mavenCentral()
}
configurations.all {
    resolutionStrategy {
//        force 'androidx.core:core-ktx:1.6.0'
//        force 'androidx.lifecycle:lifecycle-common:2.4.0'
//        force 'androidx.lifecycle:lifecycle-livedata:2.0.0'
//        force 'androidx.lifecycle:lifecycle-livedata-core:2.3.1'
//        force 'androidx.lifecycle:lifecycle-runtime:2.4.0'
//        force 'androidx.lifecycle:lifecycle-viewmodel:2.3.1'
//        force 'androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1'
    }
}
