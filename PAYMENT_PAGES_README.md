# 新增支付页面功能

## 概述
为播放器应用添加了三个新的支付页面，支持横屏显示（1080x800），包含多语言支持。

## 新增页面

### 1. 首页 (HomeActivity)
- **功能**: 支付选项选择页面
- **布局**: `activity_home.xml`
- **特性**:
  - 顶部语言切换按钮（英文、中文、德文）
  - 两个主要支付选项按钮：
    - PAY WITH TERMINAL
    - PAY WITH APP
  - 横屏显示，尺寸适配1080x800

### 2. 终端支付页面 (PayWithTerminalActivity)
- **功能**: 终端刷卡支付流程
- **布局**: `activity_pay_terminal.xml`
- **特性**:
  - 左侧：返回按钮（支付完成前禁用）+ 支付信息
  - 右侧：大字体提示信息 + 进度指示器
  - 模拟支付流程：等待 → 处理中 → 完成
  - 支付完成后自动返回或手动返回

### 3. 应用支付页面 (PayWithAppActivity)
- **功能**: 二维码扫码支付流程
- **布局**: `activity_pay_app.xml`
- **特性**:
  - 左侧：返回按钮（支付完成前禁用）+ 支付说明
  - 右侧：二维码显示区域
  - 使用现有的二维码生成逻辑
  - 支付完成后自动返回或手动返回

## 技术实现

### 多语言支持
在以下文件中添加了新的字符串资源：
- `app/src/main/res/values/strings.xml` (英文)
- `app/src/main/res/values-zh/strings.xml` (中文)
- `app/src/main/res/values-de/strings.xml` (德文)

### 新增字符串资源
```xml
<!-- 支付页面相关 -->
<string name="home_title">Payment Options</string>
<string name="pay_with_terminal">PAY WITH TERMINAL</string>
<string name="pay_with_app">PAY WITH APP</string>
<string name="back">Back</string>
<string name="language_en">English</string>
<string name="language_zh">中文</string>
<string name="language_de">Deutsch</string>
<!-- 更多字符串... -->
```

### Activity注册
在 `AndroidManifest.xml` 中注册了三个新的Activity：
```xml
<activity
    android:name="com.stwpower.player.HomeActivity"
    android:configChanges="orientation|screenSize"
    android:screenOrientation="landscape"
    android:exported="false" />
<!-- 其他Activity... -->
```

### 启动方式
从现有的MainActivity启动支付页面：
- **设备类型检查**: 只有支持的设备类型才会启用支付UI功能
- **长按触发**: 在支持的设备上，长按二维码区域触发 `launchPaymentHome()` 方法
- **自动启动**: 启动HomeActivity进入支付流程

### 设备类型配置
通过 `isPaymentUISupported()` 方法控制哪些设备支持新的支付UI：

**专用终端设备**:
- `terminal` - **专用支付终端设备（应用启动时直接进入支付UI）**

**支持的设备类型**（长按二维码启动）:
- `b6` - 6槽横屏设备
- `b6_2` - 6槽横屏设备v2
- `b12` - 12槽横屏设备
- `c12` - 12槽横屏设备（c系列）
- `b24_2` - 24槽横屏设备v2
- `b24_3` - 24槽横屏设备v3
- `b24_4` - 24槽横屏设备v4
- `b6_2_chargez` - 充电设备

**不支持的设备类型**:
- `b24` - 24槽竖屏设备
- `c24` - 24槽竖屏设备（c系列）
- `c48` - 48槽竖屏设备
- 其他未知设备类型

## 设计特点

### 布局设计
- **横屏优化**: 所有页面强制横屏显示
- **1080x800适配**: 使用相对布局和约束布局确保在目标分辨率下正确显示
- **大字体**: 支付页面使用大字体确保可读性
- **简洁界面**: 专注于支付流程，减少干扰元素

### 用户体验
- **状态管理**: 支付过程中禁用返回按钮，防止误操作
- **进度反馈**: 清晰的状态提示和进度指示
- **自动返回**: 支付完成后5秒自动返回，也可手动返回
- **语言切换**: 实时语言切换，立即生效

### 安全考虑
- **防误操作**: 支付过程中禁用返回功能
- **状态验证**: 只有支付完成后才允许返回
- **超时处理**: 模拟真实支付场景的时间控制

## 使用方法

### 方式一：专用终端设备（device_type = "terminal"）
1. **自动启动**:
   - 当设备类型配置为 "terminal" 时，应用启动后自动进入支付UI
   - 跳过原有的播放器界面，直接显示支付选项页面
   - 适用于专门的支付终端设备

### 方式二：其他支持的设备（长按启动）
1. **检查设备支持**:
   - 系统会自动检查当前设备类型是否支持支付UI
   - 只有支持的设备才会启用长按功能
   - 日志中会显示 "Payment UI enabled/disabled for device type: xxx"

2. **启动支付页面**:
   - 在支持的设备上，在主播放页面长按二维码区域
   - 进入支付选项页面

### 通用操作流程
3. **选择支付方式**:
   - 点击 "PAY WITH TERMINAL" 进入终端支付
   - 点击 "PAY WITH APP" 进入扫码支付

4. **语言切换**:
   - 在首页点击顶部的语言按钮
   - 支持英文、中文、德文切换

5. **支付流程**:
   - 按照页面提示完成支付
   - 等待支付完成后返回

## 文件清单

### 新增文件
- `app/src/main/java/com/stwpower/player/HomeActivity.kt`
- `app/src/main/java/com/stwpower/player/PayWithTerminalActivity.kt`
- `app/src/main/java/com/stwpower/player/PayWithAppActivity.kt`
- `app/src/main/res/layout/activity_home.xml`
- `app/src/main/res/layout/activity_pay_terminal.xml`
- `app/src/main/res/layout/activity_pay_app.xml`

### 修改文件
- `app/src/main/AndroidManifest.xml` (添加Activity注册)
- `app/src/main/java/com/stwpower/player/MainActivity.kt` (添加启动逻辑)
- `app/src/main/java/com/stwpower/player/base/MyApp.java` (添加语言访问方法)
- `app/src/main/res/values*/strings.xml` (添加多语言字符串)
- `gradle.properties` (增加内存设置)

## 配置说明

### 设备类型配置文件
设备类型通过配置文件设置，通常在设备的配置文件中指定：
```
device_type=terminal
```

### 支持的配置值
- `terminal` - 专用支付终端，启动时直接进入支付UI
- `b6`, `b6_2`, `b12`, `c12` 等 - 支持长按启动支付UI的设备
- `b24`, `c24`, `c48` 等 - 不支持支付UI的设备

## 注意事项

1. **设备类型配置**:
   - 新的支付UI只在配置的设备类型上启用
   - 不支持的设备保持原有功能不变
   - 可以通过修改 `isPaymentUISupported()` 方法调整支持的设备类型

2. **专用终端模式**:
   - `device_type=terminal` 时，应用启动直接进入支付UI
   - MainActivity 会立即启动 HomeActivity 并结束自身
   - 适用于专门的支付终端设备

3. **内存设置**: 已将Gradle内存设置从1.5GB增加到4GB以支持编译

4. **依赖关系**: 使用了现有的二维码生成库和工具类

5. **兼容性**:
   - 保持与现有代码的兼容性，不影响原有功能
   - 不支持的设备完全不会加载新的支付UI代码

6. **测试建议**:
   - 建议在目标设备上测试横屏显示效果和触摸响应
   - 测试不同设备类型的启用/禁用逻辑
   - 验证日志输出确认设备支持状态
   - 特别测试 `device_type=terminal` 的自动启动功能
