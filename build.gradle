// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        kotlin_version = "1.8.20"
    }
    repositories {
        google()
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url 'https://jitpack.io' }
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.6.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.blankj:bus-gradle-plugin:2.6'
    }
}

plugins {
    id 'com.google.devtools.ksp' version '1.8.10-1.0.9' apply false
}

allprojects {
    repositories {
        google()
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url 'https://jitpack.io' }
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
