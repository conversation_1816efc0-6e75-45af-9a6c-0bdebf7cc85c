plugins {
    id("com.android.library")
}

android {
    namespace = "com.nayax.emv_core.api"
    compileSdk = 34

    defaultConfig {
        minSdk = 24
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
//        consumerProguardFiles("consumer-rules.pro")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
//            proguardFiles(
//                getDefaultProguardFile("proguard-android-optimize.txt"),
//                "proguard-rules.pro"
//            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation("androidx.appcompat:appcompat:1.6.1")
//    implementation("androidx.appcompat:appcompat:1.7.0")
    implementation("com.google.android.material:material:1.10.0")
//    implementation("com.google.android.material:material:1.12.0")

    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.2.1")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.6.1")
}

// Custom Javadoc task for Android project
//tasks.register<Javadoc>("androidJavadoc") {
//    // Use the correct syntax for accessing Android source sets in Kotlin DSL
//    val mainSourceSet = android.sourceSets["main"]
//
//    // Set the source for Javadoc generation from the 'main' source set
//    source = mainSourceSet.java.getSourceFiles()
//
//    // Set the classpath including the bootClasspath and the project's Java files
//    classpath = files(android.bootClasspath) + files(mainSourceSet.java.srcDirs)
//
//    // Options for Javadoc generation
//    options {
//        encoding = "UTF-8"
//        isFailOnError = false
//        (this as StandardJavadocDocletOptions).addStringOption("Xdoclint:none", "-quiet")
//    }
//}

// Task to generate Javadoc JAR
//tasks.register<Jar>("javadocJar") {
//    dependsOn("androidJavadoc")
//    from(tasks.named("androidJavadoc"))
//    archiveClassifier.set("javadoc")
//}

// Optional: Task to generate Sources JAR
tasks.register<Jar>("sourcesJar") {
    from(android.sourceSets["main"].java.srcDirs)
    archiveClassifier.set("sources")
}

// Add the Javadoc and Sources JAR to the artifacts
//artifacts {
//    add("archives", tasks.named("javadocJar"))
//    add("archives", tasks.named("sourcesJar"))
//}
//
//// Hook javadocJar and sourcesJar tasks into the build lifecycle
//afterEvaluate {
//    tasks.named("assembleRelease").configure {
//        dependsOn(tasks.named("javadocJar"))
//        dependsOn(tasks.named("sourcesJar"))
//    }
//}