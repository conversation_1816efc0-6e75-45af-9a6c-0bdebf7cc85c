package com.nayax.emv_core.api;

import android.content.Context;

public interface IEmvCoreClient {


    boolean isEmvCoreConnected();

    /**
     * Initialize EmvCore module and connect to EmvCore service
     * @param context Application context
     */
    void Initialize(Context context);

    /**
     * Destroy EmvCore parameters and disconnect from EmvCore service
     */
    void Destroy();

    /**
     * Set callbacks called by EmvCore and implemented by the sales application regarding Payment functionality.
     * Includes TransactionComplete and ReaderMessage.
     *
     * @param iPaymentCallbacks Payment transaction Details
     */
    void SetPaymentCallbacks(IPaymentCallbacks iPaymentCallbacks);

    /**
     * Set callbacks called by EmvCore and implemented by the sales application regarding the operation of EmvCore itself.
     * Includes SystemStatusChanged event.
     *
     * @param iMaintenanceCallbacks Maintenance Details
     */
    void SetManagementCallbacks(IManagementCallbacks iMaintenanceCallbacks);

    /**
     * Set callbacks called by EmvCore and implemented by the sales application to log a message .
     *
     * @param iLogger Logger
     */
    void SetLoggerCallbacks(ILogger iLogger);

    /**
     * @return the version of the EMV Core.
     * @throws EmvCoreException
     */
    String GetEmvCoreVersion() throws EmvCoreException;

    /**
     * @return the version of the Reader firmware.
     * @throws EmvCoreException
     */
    String GetReaderVersion() throws EmvCoreException;

    /**
     * EMV Core ID (a.k.a KioskId) is a unique identifier of the Emv Core.
     *
     * @return The Emv Core
     * @throws EmvCoreException
     */
    String GetKioskID() throws  EmvCoreException;

    /**
     * Get EMV core service status.
     *
     * @throws EmvCoreException
     */
    EmvCoreStatus GetStatus() throws  EmvCoreException;

    /**
     * Show messages on oti Reader screen
     *
     * @param line1 - 1st line
     * @param line2 - 2nd line
     */
    void ShowMessage(String line1, String line2) throws EmvCoreException;

    /**
     * Get attribute from config file
     *
     * @param key - attribute key
     * @return The attribute value
     */
    String GetAttribute(String key) throws EmvCoreException;


    /**
     * Wait for a card (or phone, or watch, etc.) read it, and authorize a transaction with the payment processor.
     * EMV Core will wait for a card to be presented to the POS, Read the card, and process it with the payment service if needed.
     * It will then send a com.nayax.pos.IPaymentCallback.TransactionComplete() callback.
     * To complete the transaction, the application must call ConfirmTransaction() or VoidTransaction() method.
     *
     * @param amountMinorUnits the amount to charge the card, in Minor units (e.g. Cents - not Dollars).
     * @param currency         Currency to use in the transaction.
     * @param productID        ID of the product that was purchased
     * @param timeoutSeconds   timeout in seconds, 0 means no timeout. must be non-negative. if a card was not presented during this time
     *                         them EMV Core will send a com.nayax.pos.IPaymentCallback.TransactionComplete() callback indicating timeout.
     * @param continuous        If continuous is true, then poll for card will start immediately after card transaction timeout and
     *                          TransactionComplete evet will be sends with status PollContinuous
     */
    void PreAuthorize(int amountMinorUnits, int currency, int productID, int timeoutSeconds,boolean continuous) throws EmvCoreException;

    /**
     * Execute a single step card transaction (as opposed to 2 step transaction done with the PreAuthorize and ConfirmTransaction commands).
     * EMV-core will wait for a card to be presented to the Reader, Read the card, and process it with the payment service if needed.
     * It will then send a TransactionComplete() callback.
     * The application can cancel the last transaction by calling the VoidTransaction()  method before another transaction has been performed.
     *
     * @param amountMinorUnits the amount to charge the card, in Minor units (e.g. Cents, not Dollars).
     * @param currency         Currency to use in the transaction.
     * @param timeoutSeconds   timeout in seconds, 0 means no timeout. must be non-negative. if a card was not presented during this time
     *                         them EMV Core will send a com.nayax.emv_core.IPaymentCallback.TransactionComplete() callback indicating timeout.
     * @param productID        ID of the product that was purchased
     *                         them EMV Core will send a com.nayax.pos.IPaymentCallback.TransactionComplete() callback indicating timeout.
     * @param continuous        If continuous is true, then poll for card will start immediately after card transaction timeout and
     *                          TransactionComplete evet will be sends with status PollContinuous
     */
    void PayTransaction(int amountMinorUnits, int currency, int productID, int timeoutSeconds,boolean continuous) throws EmvCoreException;

    /**
     * Start polling with get token
     *
     * @param timeoutSeconds Timeout to transaction complete
     */
    void GetCardToken(int timeoutSeconds, boolean continuous) throws EmvCoreException;

    /**
     * A confirmation request is the second stage to an authorization request.
     * It is used to confirm the authorization was obtained so that settlement can take place.
     * EMV Core will send the confirmation request (settlement) to the server, in the background,
     * without blocking the Sales-App from starting another transaction.
     *
     * @param transaction_Reference  identifier of this transaction - received in com.nayax.pos.IPaymentCallback.TransactionComplete() callback
     * @param actualAmountMinorUnits Actual transaction amount in minor units (e.g. Cents). may be different than amount from PreAuthorize()
     * @param productID              ID of the product that was purchased
     */
    void ConfirmTransaction(String transaction_Reference, int actualAmountMinorUnits, int productID) throws EmvCoreException;

    /**
     * Tells the POS to stop searching for card.
     * Calling this will stop the execution of AuthorizeTransaction ot PayTransaction.
     * This method is blocking until cancellation was accepted and EMV core is ready for further requests.
     */
    void CancelTransaction() throws EmvCoreException;

    /**
     * Close a transaction that was already approved without charging the consumer.
     * A Void Transaction request is used to void the last transaction that has been done in 2 cases:
     * A. 	AuthorizeTransaction() succeeded but ConfirmTransaction() was not called yet.
     * in order to release funds that have been reserved against an account when the transaction will no longer take place.
     * B. 	Transaction has been done using PayTransaction() but needs to be canceled (e.g. when product has not been vended)
     *
     * @param transaction_Reference identifier of this transaction - received in TransactionComplete() callback
     */
    void VoidTransaction(String transaction_Reference) throws EmvCoreException;



}
