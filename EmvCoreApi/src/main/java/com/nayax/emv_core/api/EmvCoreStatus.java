package com.nayax.emv_core.api;

public enum EmvCoreStatus
{
    /**
     * Not Ready to work. reasons can be that Reader is not connected or not accessible, configuration problems, etc.
     */
    NotReady,

    /**
     * Ready to accept Transaction requests.
     */
    Ready,

    /**
     * During payment request - meaning some functions may be refused.
     */
    PaymentTransaction,

    /**
     * updating the Reader - so temporarily not ready.
     */
    Update,

    /**
     * reader not communicating or not connected
     */
    NoReader,

    /**
     * Emv<PERSON>ore doesn't know the Terminal ID
     */
    NoTerminalId,

    /**
     * No transaction
     */
    NoTransaction;
}
