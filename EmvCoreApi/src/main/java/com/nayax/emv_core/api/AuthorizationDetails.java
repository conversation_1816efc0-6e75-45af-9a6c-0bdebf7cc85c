package com.nayax.emv_core.api;

import java.math.BigDecimal;

public class AuthorizationDetails {

    public final String ErrorDescription;
    public final Integer ErrorCode;
    public final String Status;
    public final BigDecimal AmountAuthorized;
    public final BigDecimal AmountRequested;
    public final String  AuthorizationCode;
    public final String  PartialPan;
    public final String  CardType;
    public final String  AuthID;
    public final String  ReceiptID;
    public final String  Channel;
    public final String  AID;
    public final String  TVR;
    public final String  IAD;
    public final String  TSI;
    public final String  ARC;
    public final String  TransactionTime;
    public final String  Application_Label;
    public final String  Currency;
    public final String  CVM;
    public final boolean  IsTransactionApproved;
    public final String  CardToken;
    public final String  Additional_Parameters;
    public final String Transaction_Reference;
    public final String  RRN;
    public final String  EntryMode;
    public final String  Card_ID;
    public final BigDecimal Card_Balance;

    public AuthorizationDetails(
            String errorDescription, int errorCode, String status, BigDecimal amountAuthorized,
            BigDecimal amountRequested, String authorizationCode, String partialPan, String cardType,
            String authID, String receiptID, String channel, String AID, String TVR, String IAD, String TSI,
            String ARC, String transactionTime, String application_Label, String currency, String CVM,
            boolean isTransactionApproved, String cardToken, String additional_Parameters,
            String transaction_Reference, String RRN, String entryMode, String card_ID, BigDecimal card_Balance
    ) {
        this.ErrorDescription = errorDescription;
        this.ErrorCode = errorCode;
        this.Status = status;
        AmountAuthorized = amountAuthorized;
        AmountRequested = amountRequested;
        AuthorizationCode = authorizationCode;
        PartialPan = partialPan;
        CardType = cardType;
        AuthID = authID;
        ReceiptID = receiptID;
        Channel = channel;
        this.AID = AID;
        this.TVR = TVR;
        this.IAD = IAD;
        this.TSI = TSI;
        this.ARC = ARC;
        TransactionTime = transactionTime;
        Application_Label = application_Label;
        Currency = currency;
        this.CVM = CVM;
        IsTransactionApproved = isTransactionApproved;
        CardToken = cardToken;
        Additional_Parameters = additional_Parameters;
        Transaction_Reference = transaction_Reference;
        this.RRN = RRN;
        EntryMode = entryMode;
        Card_ID = card_ID;
        Card_Balance = card_Balance;
    }

}
