package com.nayax.emv_core.api;

public interface IPaymentCallbacks {
    /**
     * Called by EmvCore when Card Transaction is complete after command PreAuthorize or PayTransaction.
     * May indicate approval or decline.
     * If transactions requires extra steps like an online approval or a retry by the consumer -
     * this function is called after these extra steps are complete.
     *
     * @param authorizationDetails Payment transaction details
     */
    void TransactionComplete(AuthorizationDetails authorizationDetails);

    /**
     * Called by EmvCore when Get card token is complete after command GetCardToken.
     *
     * @param tokenDetails Get card token details
     */
    void CardTokenReceived(TokenDetails tokenDetails);

    /**
     * Called by EmvCore when it needs to display a message to the consumer.
     * When the Reader has no Display - the Sales application is responsible to display those messages to the consumer.
     *
     * @param readerMessage Message ID and Text
     */
    void ReaderMessage(ReaderMessage readerMessage);
}
