# Player
### 版本说明：

#### V4.00-35
> 优化nayax(刷卡后需要等一分钟才能收到回调的问题)
> 竖屏增加无二维码的ui

#### V4.00-34
> 减少接口请求，主动请求广告改为被动请求(RK3288NEW和SC20NEW)

#### V4.0.0-33
> bug修复

#### V4.0.0-32
> 奔溃重启功能

#### V4.0.0-31
> 部分设备视频文件路径问题修复

#### V4.0.0-28
> 优化

#### V4.0.0-27
> 优化弹窗消息，从本地文件中获取
> 增加归还提示
> 优化弹窗大小和文本大小
> 创建paymentIntent传入qrCode（用于传入customerId）

#### V4.0.0-26
> nayax sdk升级
> 其它优化

#### V4.0.0-25
> 增加屏幕亮度按规则修改功能

#### V4.0.0-24
> 优化检查更新，从对应的服务端检查是否更新

#### V4.0.0-23
> 接入nayax

#### V4.0.0-22
> terminal弹窗国际化

#### V4.0.0（2024.4.8）
> 版本变更为v4，与v3不带pos功能区分开
> 稳定性测试基本ok，优化了突然出现的证书问题

#### V3.6.5（2023.12.28）
> 接入Stripe Terminal，配置新增 secretKey:appSecretKey（同时在后台需要设置设备的位置id）
> 优化（2023.12.30）

#### V3.6.4（2023.12.24）
> 新增配置b6_2_chargez(修改二维码位置，客户定制需求)

#### V3.6.3（2023.9.22）
> 7寸屏二维码位置向右移动

#### V3.6.2（2023.3.15）
> 修复3288播放下载好的视频出现绿屏问题，除了SC20使用系统内核，其它设备使用EXOPlayer内核
> 修复url类型不能设置图片问题


#### V3.6.1（2022.6.20）
> 新增配置b6_2(b设备新系统二维码字体大小)

#### V3.6（12.10）
> 切换内核
> 
> 修复循环播放问题

#### V3.5（12.08）
> 提取公共配置，从配置文件中读取
> 修复重启奔溃bug
> 修复获取二维码bug

#### V3.4（11.05）
> 实现无缝播放

